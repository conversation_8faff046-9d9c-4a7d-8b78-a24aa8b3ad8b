import{r as j,u,j as s,ar as n}from"./index-DPaefDjm.js";import{S as m}from"./SellerLayout-k6HMxGdv.js";import{a as p}from"./index-BksfLEdQ.js";import{a as N}from"./index-Cw39eGqq.js";import{S as r}from"./index-DFZNVNT1.js";const v=[{count:"08",label:"Total Strategies",color:"purple"},{count:"02",label:"Requests",color:"orange"},{count:"03",label:"Bids",color:"green"}],d=[{id:1,title:"<PERSON> and Coaching Philosophies...",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0},{id:2,title:"John Calipari - Early Transition Offensive Concepts",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1}],h=[{id:"#2345678",title:d[0].title,date:d[0].date,price:d[0].price,amount:"$19.00",user:"<PERSON>"},{id:"#2345679",title:d[1].title,date:d[1].date,price:d[1].price,amount:"$18.00",user:"Olivia Smart"}],b=[...h],g=({count:e,label:t,color:c})=>s.jsxs("div",{className:`stats-card ${c}`,children:[s.jsx("h2",{children:e}),s.jsx("p",{children:t})]}),S=({item:e,toggle:t,onViewDetails:c})=>s.jsxs("tr",{children:[s.jsx("td",{children:e.id}),s.jsx("td",{children:s.jsxs("div",{className:"video-title",children:[s.jsx(n,{className:"video-icon"}),s.jsx("span",{children:e.title})]})}),s.jsx("td",{children:e.date}),s.jsx("td",{children:e.price}),s.jsx("td",{children:s.jsxs("label",{className:"switch",children:[s.jsx("input",{type:"checkbox",checked:e.status,onChange:()=>t(e.id)}),s.jsx("span",{className:"slider round"})]})}),s.jsx("td",{children:s.jsx("div",{className:"action-icons",children:s.jsx(r,{className:"action-icon",onClick:()=>c(e.id)})})})]}),w=({item:e,showUser:t=!0})=>s.jsxs("tr",{children:[s.jsx("td",{children:e.id}),s.jsx("td",{children:s.jsxs("div",{className:"video-title",children:[s.jsx(n,{className:"video-icon"}),s.jsx("span",{children:e.title})]})}),s.jsx("td",{children:e.date}),s.jsx("td",{children:e.price}),s.jsx("td",{children:e.amount}),t?s.jsx("td",{children:e.user}):null,s.jsx("td",{children:s.jsxs("div",{className:"action-icons",children:[s.jsx(r,{className:"action-icon"}),s.jsx(N,{className:"action-icon"})]})})]}),f=({item:e})=>s.jsxs("tr",{children:[s.jsx("td",{children:e.id}),s.jsx("td",{children:s.jsxs("div",{className:"video-title",children:[s.jsx(n,{className:"video-icon"}),s.jsx("span",{children:e.title})]})}),s.jsx("td",{children:e.date}),s.jsx("td",{children:e.price}),s.jsx("td",{children:e.amount}),s.jsx("td",{children:s.jsx("div",{className:"action-icons",children:s.jsx(p,{className:"action-icon"})})})]}),q=()=>{const[e,t]=j.useState(d),c=u(),o=i=>{t(l=>l.map(a=>a.id===i?{...a,status:!a.status}:a))},x=i=>{c(`/seller/strategy-details/${i}`)};return s.jsx(m,{children:s.jsxs("div",{className:"dashboard-container",children:[s.jsx("div",{className:"stats-container",children:v.map((i,l)=>s.jsx(g,{...i},l))}),s.jsxs("div",{className:"section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{children:"My Sports Strategies"}),s.jsx("a",{href:"/downloads",children:"View All Downloads"})]}),s.jsx("div",{className:"table-container",children:s.jsxs("table",{children:[s.jsx("thead",{children:s.jsxs("tr",{children:[s.jsx("th",{children:"No."}),s.jsx("th",{children:"Videos/Documents"}),s.jsx("th",{children:"Date"}),s.jsx("th",{children:"Price"}),s.jsx("th",{children:"Status"}),s.jsx("th",{children:"Action"})]})}),s.jsx("tbody",{children:e.map(i=>s.jsx(S,{item:i,toggle:o,onViewDetails:x},i.id))})]})})]}),s.jsxs("div",{className:"section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{children:"New Requests"}),s.jsx("a",{href:"/requests",children:"View All Requests"})]}),s.jsx("div",{className:"table-container",children:s.jsxs("table",{children:[s.jsx("thead",{children:s.jsxs("tr",{children:[s.jsx("th",{children:"Order Id"}),s.jsx("th",{children:"Video/Documents"}),s.jsx("th",{children:"Date"}),s.jsx("th",{children:"Price"}),s.jsx("th",{children:"Requested Amount"}),s.jsx("th",{children:"Requested Customer"}),s.jsx("th",{children:"Action"})]})}),s.jsx("tbody",{children:h.map(i=>s.jsx(w,{item:i,showUser:!0},i.id))})]})})]}),s.jsxs("div",{className:"section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{children:"New Bids"}),s.jsx("a",{href:"/bids",children:"View All Bids"})]}),s.jsx("div",{className:"table-container",children:s.jsxs("table",{children:[s.jsx("thead",{children:s.jsxs("tr",{children:[s.jsx("th",{children:"Bid Id"}),s.jsx("th",{children:"Video/Documents"}),s.jsx("th",{children:"Date"}),s.jsx("th",{children:"Price"}),s.jsx("th",{children:"Bid Amount"}),s.jsx("th",{children:"Action"})]})}),s.jsx("tbody",{children:b.map(i=>s.jsx(f,{item:i},i.id))})]})})]})]})})};export{q as default};
