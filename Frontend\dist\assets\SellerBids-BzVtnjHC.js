import{e as c,ay as d,u as m,j as a}from"./index-DPaefDjm.js";import{S as b}from"./SellerLayout-k6HMxGdv.js";import{T as u}from"./Table-K41WFawU.js";import{S as p}from"./index-DFZNVNT1.js";const N=()=>{const l=c(d),t=m(),i=e=>{t(`/seller/bid-details/${e.replace("#","")}`)},o=[{key:"no",label:"No.",className:"no"},{key:"id",label:"Bid Id"},{key:"content",label:"Videos/Documents",render:e=>a.jsxs("div",{className:"video-doc",children:[a.jsx("img",{src:e.image,alt:e.title}),a.jsx("span",{children:e.title})]})},{key:"date",label:"Date"},{key:"price",label:"Price"},{key:"bidAmount",label:"Bid Amount"},{key:"action",label:"Action",render:e=>a.jsx("div",{className:"action-icon-container",children:a.jsx(p,{className:"threedoticon",onClick:()=>i(e.id),style:{cursor:"pointer"}})})}],n=e=>e.map((s,r)=>({...s,no:r+1,date:`${s.date} | 4:50PM`}));return a.jsx(b,{children:a.jsx("div",{className:"seller-bids-container",children:a.jsx(u,{columns:o,data:n(l),className:"bids-table"})})})};export{N as default};
