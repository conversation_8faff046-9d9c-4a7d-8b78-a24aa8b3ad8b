import{G as P,u as M,r as c,j as e}from"./index-DPaefDjm.js";function I(d){return P({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M80 0C44.7 0 16 28.7 16 64l0 384c0 35.3 28.7 64 64 64l224 0c35.3 0 64-28.7 64-64l0-384c0-35.3-28.7-64-64-64L80 0zm80 432l64 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-64 0c-8.8 0-16-7.2-16-16s7.2-16 16-16z"},child:[]}]})(d)}const $=()=>{const d=M(),[i,p]=c.useState({phone:"",countryCode:"+91"}),[t,x]=c.useState({}),[v,h]=c.useState(!1),[l,m]=c.useState(["","","","","",""]),[u,r]=c.useState(""),[f,N]=c.useState(!1),g=s=>{const{name:a,value:n,type:o,checked:w}=s.target;p({...i,[a]:o==="checkbox"?w:n}),t[a]&&x({...t,[a]:null})},y=s=>{p({...i,countryCode:s.target.value})},b=()=>{const s={};return i.phone.trim()?/^\d{10}$/.test(i.phone)||(s.phone="Phone number must be 10 digits"):s.phone="Phone number is required",s},k=(s,a)=>{if(a.length>1)return;const n=[...l];if(n[s]=a,m(n),a&&s<5){const o=document.getElementById(`code-${s+1}`);o&&o.focus()}u&&r("")},C=(s,a)=>{if(a.key==="Backspace"&&!l[s]&&s>0){const n=document.getElementById(`code-${s-1}`);n&&n.focus()}},S=async s=>{s.preventDefault();const a=b();if(Object.keys(a).length>0){x(a);return}h(!0),console.log("Verification code sent to:",i.countryCode+i.phone)},D=()=>{const s=l.join("");if(s.length!==6){r("Please enter the complete verification code");return}console.log("Verifying code:",s),s.length===6?(h(!1),m(["","","","","",""]),r(""),N(!0),console.log("Verification successful! Proceeding to checkout...")):r("Invalid verification code")},V=()=>{h(!1),m(["","","","","",""]),r("")},j=()=>{const s={orderId:"#"+Math.random().toString(36).substring(2,10).toUpperCase(),date:new Date().toLocaleDateString("en-US",{day:"numeric",month:"short",year:"numeric"}),time:new Date().toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0}),items:1,totalAmount:"$22.00",customerDetails:{name:"John Smith",email:"<EMAIL>",phone:i.countryCode+" "+i.phone},paymentDetails:{cardNumber:"•••• •••• •••• 1234",method:"Mastercard"},itemInfo:{title:"Frank Martin - Drills and Coaching Philosophy to Developing Toughness in Basketball",category:"Basketball",image:"https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG"}};d("/thank-you",{state:{orderData:s}})};return e.jsxs("div",{className:"checkout-page",children:[e.jsx("div",{className:"max-container",children:e.jsxs("div",{className:"checkout-content",children:[f?e.jsx("div",{className:"checkout-left",children:e.jsxs("div",{className:"checkout-form-container",children:[e.jsx("h1",{className:"checkout-title",children:"Checkout"}),e.jsxs("div",{className:"leftborderdiv",children:[e.jsx("div",{className:"checkout-success",children:"✅ Phone number verified successfully!"}),e.jsxs("div",{className:"checkout-section",children:[e.jsx("h2",{className:"section-title",children:"Saved Cards"}),e.jsxs("div",{className:"saved-cards",children:[e.jsxs("div",{className:"card-option",children:[e.jsx("input",{type:"radio",id:"card1",name:"payment",defaultChecked:!0}),e.jsx("label",{htmlFor:"card1",className:"card-label",children:e.jsxs("div",{className:"card-info",children:[e.jsx("span",{className:"card-type",children:"Mastercard"}),e.jsx("span",{className:"card-number",children:"•••• •••• •••• 1234"})]})})]}),e.jsxs("div",{className:"card-option",children:[e.jsx("input",{type:"radio",id:"card2",name:"payment"}),e.jsx("label",{htmlFor:"card2",className:"card-label",children:e.jsxs("div",{className:"card-info",children:[e.jsx("span",{className:"card-type",children:"Mastercard"}),e.jsx("span",{className:"card-number",children:"•••• •••• •••• 1234"})]})})]}),e.jsxs("div",{className:"card-option",children:[e.jsx("input",{type:"radio",id:"card3",name:"payment"}),e.jsx("label",{htmlFor:"card3",className:"card-label",children:e.jsxs("div",{className:"card-info",children:[e.jsx("span",{className:"card-type",children:"Mastercard"}),e.jsx("span",{className:"card-number",children:"•••• •••• •••• 1234"})]})})]})]}),e.jsx("h3",{className:"section-title",children:"Payment Method"}),e.jsxs("div",{className:"payment-form",children:[e.jsxs("div",{className:"form-row",children:[e.jsx("input",{type:"text",placeholder:"Name on card",className:"form-input"}),e.jsx("input",{type:"text",placeholder:"Card Number",className:"form-input"})]}),e.jsxs("div",{className:"form-row",children:[e.jsx("input",{type:"text",placeholder:"Exp. Date",className:"form-input"}),e.jsx("input",{type:"text",placeholder:"CVV",className:"form-input"})]})]}),e.jsx("button",{className:"btn-primary place-order-main-btn",onClick:j,children:"Place Order"})]})]})]})}):e.jsx("div",{className:"checkout-left",children:e.jsxs("div",{className:"checkout-form-container",children:[e.jsx("h1",{className:"checkout-title",children:"Checkout"}),e.jsxs("div",{className:"leftborderdiv",children:[e.jsx("div",{className:"checkout-alert",children:"Please log in to purchase the content!"}),e.jsxs("div",{className:"signin-section",children:[e.jsxs("div",{className:"signin-sectioncontainer",children:[e.jsx("h2",{className:"signin-title",children:"Sign In"}),e.jsxs("p",{className:"signin-subtitle",children:["Don't have an account?"," ",e.jsx("a",{href:"/signup",className:"signup-link",children:"Sign Up"})]})]}),e.jsxs("form",{onSubmit:S,className:"signin-form",children:[e.jsxs("div",{className:"auth-form-input form-input-container",children:[e.jsxs("div",{className:"phone-input-wrapper",children:[e.jsx("div",{children:e.jsxs("div",{className:"country-code-select",children:[e.jsx(I,{style:{color:"var(--dark-gray)"}}),e.jsxs("select",{value:i.countryCode,onChange:y,className:"selectstylesnone",children:[e.jsx("option",{value:"+91",children:"+91"}),e.jsx("option",{value:"+1",children:"+1"}),e.jsx("option",{value:"+44",children:"+44"}),e.jsx("option",{value:"+61",children:"+61"}),e.jsx("option",{value:"+86",children:"+86"}),e.jsx("option",{value:"+49",children:"+49"}),e.jsx("option",{value:"+33",children:"+33"}),e.jsx("option",{value:"+81",children:"+81"}),e.jsx("option",{value:"+7",children:"+7"}),e.jsx("option",{value:"+55",children:"+55"})]})]})}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:i.phone,onChange:s=>{const a=s.target.value.replace(/\D/g,"");g({target:{name:"phone",value:a}})},placeholder:"Enter Phone Number",className:`form-input phone-input ${t.phone?"input-error":""}`,required:!0,pattern:"[0-9]*"})]}),t.phone&&e.jsx("p",{className:"error-message",children:t.phone})]}),e.jsx("button",{type:"submit",className:"btn-primary verifybtnwidth",children:"Send Verification Code"})]})]})]})]})}),e.jsx("div",{className:"checkout-right",children:e.jsxs("div",{className:"order-summary",children:[e.jsx("h2",{className:"order-title",children:"Your Order"}),e.jsxs("div",{className:"rightbackgrounddiv",children:[e.jsxs("div",{className:"item-info-section",children:[e.jsx("h3",{className:"item-info-title",children:"Item Info"}),e.jsxs("div",{className:"item-details",children:[e.jsx("div",{className:"item-image",children:e.jsx("img",{src:"https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG",alt:"",className:"item-thumbnail"})}),e.jsx("div",{className:"item-description",children:e.jsx("h4",{className:"item-name",children:"Frank Martin - Drills and Coaching Philosophy to Developing Toughness in Basketball"})})]})]}),e.jsxs("div",{className:"pricing-section",children:[e.jsxs("div",{className:"price-row",children:[e.jsx("span",{className:"price-label",children:"Subtotal"}),e.jsx("span",{className:"price-value",children:"$22.00"})]}),e.jsxs("div",{className:"price-row total-row",children:[e.jsx("span",{className:"price-label",children:"Total"}),e.jsx("span",{className:"price-value",children:"$22.00"})]})]}),e.jsx("button",{className:"place-order-btn btn-primary",onClick:j,children:"Place Order & Download"})]})]})})]})}),v&&e.jsx("div",{className:"verification-modal-overlay",children:e.jsx("div",{className:"verification-modal",children:e.jsxs("div",{className:"verification-modal-content",children:[e.jsx("h2",{className:"verification-title",children:"Code Verification"}),e.jsxs("p",{className:"verification-subtitle",children:["Enter the Code Sent To ",i.countryCode," ",i.phone.replace(/(\d{3})(\d{3})(\d{4})/,"($1) $2-$3")]}),e.jsx("div",{className:"verification-code-container",children:l.map((s,a)=>e.jsx("input",{id:`code-${a}`,type:"text",value:s,onChange:n=>k(a,n.target.value),onKeyDown:n=>C(a,n),className:"verification-code-input",maxLength:"1",pattern:"[0-9]*",inputMode:"numeric"},a))}),u&&e.jsx("p",{className:"verification-error",children:u}),e.jsxs("p",{className:"verification-resend",children:["Didn't Received Any Code? ",e.jsx("span",{className:"resend-link",children:"Resend"})]}),e.jsxs("div",{className:"verification-buttons",children:[e.jsx("button",{onClick:D,className:"btn-primary verification-submit-btn",children:"Verify"}),e.jsx("button",{onClick:V,className:"verification-back-btn",children:"Back To Page"})]})]})})})]})};export{$ as default};
