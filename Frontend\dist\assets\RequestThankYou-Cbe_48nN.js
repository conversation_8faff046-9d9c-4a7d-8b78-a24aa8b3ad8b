import{u as i,j as e,Q as n,K as c}from"./index-DPaefDjm.js";import{t as d}from"./thankyou-zEUM9FAJ.js";const u=()=>{const a=i(),s={requestId:"#********",title:"Basketball Training Fundamentals",sport:"Basketball",contentType:"Video Tutorial",budget:"$150.00",date:"20 May 2025 | 4:50PM",status:"Pending",description:"Looking for comprehensive basketball training videos covering basic fundamentals, shooting techniques, and defensive strategies for beginner to intermediate players.",seller:"Coach <PERSON>",sellerRating:"4.8",estimatedDelivery:"3-5 business days"},l=()=>{a("/buyer/account/requests")},t=()=>{a("/")};return e.jsx("div",{className:"request-thank-you-page",children:e.jsxs("div",{className:"request-thank-you-container max-container",children:[e.jsxs("div",{className:"success-header",children:[e.jsx("div",{className:"success-icon",children:e.jsx("img",{src:d,alt:"Thank you"})}),e.jsx("h1",{className:"success-title",children:"Your request is submitted successfully!"}),e.jsx("p",{className:"success-message",children:"We will update you for the request status soon via Email or SMS."})]}),e.jsxs("div",{className:"request-info-card",children:[e.jsx("h2",{className:"request-info-title",children:"Request Information"}),e.jsxs("div",{className:"request-details-grid",children:[e.jsxs("div",{className:"request-details-grid-container",children:[e.jsxs("div",{className:"request-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Request Id:"}),e.jsx("span",{className:"detail-value",children:s.requestId})]}),e.jsxs("div",{className:"request-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Budget:"}),e.jsx("span",{className:"detail-value",children:s.budget})]}),e.jsxs("div",{className:"request-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Date:"}),e.jsx("span",{className:"detail-value",children:s.date})]}),e.jsxs("div",{className:"request-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Status:"}),e.jsx("span",{className:"detail-value status-pending",children:s.status})]}),e.jsxs("div",{className:"request-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Sport:"}),e.jsx("span",{className:"detail-value",children:s.sport})]}),e.jsxs("div",{className:"request-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Content Type:"}),e.jsx("span",{className:"detail-value",children:s.contentType})]})]}),e.jsx("div",{className:"vertical-line"}),e.jsxs("div",{className:"seller-info-section",children:[e.jsxs("div",{className:"seller-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Seller:"}),e.jsx("span",{className:"detail-value",children:s.seller})]}),e.jsxs("div",{className:"seller-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Rating:"}),e.jsxs("span",{className:"detail-value rating",children:[s.sellerRating," ⭐"]})]}),e.jsxs("div",{className:"seller-detail-item",children:[e.jsx("span",{className:"detail-label",children:"Est. Delivery:"}),e.jsx("span",{className:"detail-value",children:s.estimatedDelivery})]})]})]}),e.jsxs("div",{className:"request-details-section",children:[e.jsx("h3",{className:"section-title",children:"Request Details"}),e.jsx("div",{className:"request-content",children:e.jsx("div",{className:"request-info-content",children:e.jsxs("div",{className:"request-title-section",children:[e.jsx("h4",{className:"request-title",children:s.title}),e.jsx("p",{className:"request-description",children:s.description})]})})})]}),e.jsxs("div",{className:"action-buttons",children:[e.jsxs("button",{type:"button",className:"btn btn-outline request-btn",onClick:l,children:[e.jsx(n,{})," View My Requests"]}),e.jsxs("button",{type:"button",className:"btn btn-primary homepage-btn",onClick:t,children:[e.jsx(c,{})," Go To Homepage"]})]})]})]})})};export{u as default};
