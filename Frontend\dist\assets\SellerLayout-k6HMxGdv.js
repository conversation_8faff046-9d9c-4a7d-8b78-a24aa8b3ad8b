import{d as j,u as _,e as u,aL as g,j as e,V as p,aM as y,X as N,Y as v,Z as k,W as f,$ as C,aN as A,a1 as q,G as L,o as M,r as D}from"./index-DPaefDjm.js";const B=()=>{const t=j(),a=_(),s=u(g),r=i=>{switch(t(A(i)),i){case"dashboard":a("/seller/dashboard");break;case"my-sports-strategies":a("/seller/my-sports-strategies");break;case"requests":a("/seller/requests");break;case"bids":a("/seller/bids");break;case"cards":a("/seller/cards");break;case"profile":a("/seller/profile");break;default:a("/seller/dashboard")}},d=()=>{t(q()),a("/")};return e.jsx("div",{className:"SellerSidebar",children:e.jsx("div",{className:"SellerSidebar__container",children:e.jsxs("ul",{className:"SellerSidebar__menu",children:[e.jsxs("li",{className:`SellerSidebar__item ${s==="dashboard"?"active":""}`,onClick:()=>r("dashboard"),children:[e.jsx(p,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Dashboard"})]}),e.jsxs("li",{className:`SellerSidebar__item ${s==="my-sports-strategies"?"active":""}`,onClick:()=>r("my-sports-strategies"),children:[e.jsx(y,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"My Sports Strategies"})]}),e.jsxs("li",{className:`SellerSidebar__item ${s==="requests"?"active":""}`,onClick:()=>r("requests"),children:[e.jsx(N,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Requests"})]}),e.jsxs("li",{className:`SellerSidebar__item ${s==="bids"?"active":""}`,onClick:()=>r("bids"),children:[e.jsx(v,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Bids"})]}),e.jsxs("li",{className:`SellerSidebar__item ${s==="cards"?"active":""}`,onClick:()=>r("cards"),children:[e.jsx(k,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"My Cards"})]}),e.jsxs("li",{className:`SellerSidebar__item ${s==="profile"?"active":""}`,onClick:()=>r("profile"),children:[e.jsx(f,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"My Profile"})]}),e.jsxs("li",{className:"SellerSidebar__item SellerSidebar__logout",onClick:d,children:[e.jsx(C,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Logout"})]})]})})})};function c(t){return L({attr:{viewBox:"0 0 1024 1024"},child:[{tag:"path",attr:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 0 0 0 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"},child:[]}]})(t)}const P=({children:t})=>{const a=j(),s=M(),r=u(g),d={"/seller/dashboard":"dashboard","/seller/my-sports-strategies":"my-sports-strategies","/seller/my-sports-strategies/add":"my-sports-strategies","/seller/requests":"requests","/seller/bids":"bids","/seller/cards":"cards","/seller/profile":"profile"},i=()=>s.pathname.startsWith("/seller/strategy-details/")?"my-sports-strategies":s.pathname.startsWith("/seller/request-details/")?"requests":s.pathname.startsWith("/seller/bid-details/")?"bids":null,o=s.pathname==="/seller/my-sports-strategies/add",h=s.pathname.startsWith("/seller/strategy-details/"),b=s.pathname.startsWith("/seller/request-details/"),m=s.pathname.startsWith("/seller/bid-details/"),x={dashboard:{title:"Dashboard",icon:e.jsx(p,{})},"my-sports-strategies":{title:"My Sports Strategies",icon:e.jsx(y,{})},requests:{title:"Requests",icon:e.jsx(N,{})},bids:{title:"Bids",icon:e.jsx(v,{})},cards:{title:"My Cards",icon:e.jsx(k,{})},profile:{title:"My Profile",icon:e.jsx(f,{})}},S=x[r]||x.dashboard;D.useEffect(()=>{const n=d[s.pathname]||i();n&&n!==r&&a(A(n))},[s.pathname,r,a]);const l=_();return e.jsx("div",{className:"SellerLayout",children:e.jsxs("div",{className:"container max-container",children:[e.jsx("div",{className:"sidebar",children:e.jsx(B,{})}),e.jsxs("div",{className:"outerdiv",children:[!o&&!h&&!b&&!m&&e.jsxs("div",{className:"bordrdiv mb-30",children:[e.jsxs("h2",{className:"SellerLayout__title",children:[S.icon,S.title]}),r==="my-sports-strategies"&&e.jsx("button",{className:"add-strategy-btn btn btn-outline ",onClick:()=>l("/seller/my-sports-strategies/add"),children:"Add New Strategy"})]}),o&&e.jsx("div",{className:"bordrdiv mb-30",children:e.jsxs("div",{className:"AddStrategy__header-container",children:[e.jsxs("button",{className:"AddStrategy__back-btn",onClick:()=>l("/seller/my-sports-strategies"),children:[e.jsx(c,{className:"AddStrategy__back-icon"}),"Back"]}),e.jsx("h3",{children:"Add New Strategy"})]})}),h&&e.jsx("div",{className:"bordrdiv mb-30",children:e.jsxs("div",{className:"AddStrategy__header-container",children:[e.jsxs("button",{className:"AddStrategy__back-btn",onClick:()=>l("/seller/my-sports-strategies"),children:[e.jsx(c,{className:"AddStrategy__back-icon"}),"Back"]}),e.jsx("h3",{children:"Details Page"})]})}),b&&e.jsx("div",{className:"bordrdiv mb-30",children:e.jsxs("div",{className:"AddStrategy__header-container",children:[e.jsxs("button",{className:"AddStrategy__back-btn",onClick:()=>l("/seller/requests"),children:[e.jsx(c,{className:"AddStrategy__back-icon"}),"Back"]}),e.jsx("h3",{children:"Request Details"})]})}),m&&e.jsx("div",{className:"bordrdiv mb-30",children:e.jsxs("div",{className:"AddStrategy__header-container",children:[e.jsxs("button",{className:"AddStrategy__back-btn",onClick:()=>l("/seller/bids"),children:[e.jsx(c,{className:"AddStrategy__back-icon"}),"Back"]}),e.jsx("h3",{children:"Bid Details"})]})}),e.jsx("div",{className:"content",children:e.jsx("div",{className:"SellerLayout__content",children:t})})]})]})})};export{P as S};
