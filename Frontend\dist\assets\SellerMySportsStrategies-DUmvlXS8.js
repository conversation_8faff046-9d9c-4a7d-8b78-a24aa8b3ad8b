import{r as u,u as p,j as e}from"./index-DPaefDjm.js";import{S as h}from"./SellerLayout-k6HMxGdv.js";import{T as m}from"./Table-K41WFawU.js";import{S as g}from"./index-DFZNVNT1.js";const y=[{id:1,title:"<PERSON> and Coaching Philosophies...",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0,thumbnail:"video-1.jpg"},{id:2,title:"<PERSON> - Early Transition Offensive Concepts",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0,thumbnail:"video-2.jpg"},{id:3,title:`WR Fundamentals
PoA - <PERSON>`,date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1,thumbnail:"video-3.jpg"},{id:4,title:"<PERSON> and Coaching Philosophies...",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0,thumbnail:"video-1.jpg"},{id:5,title:"<PERSON> <PERSON>ipari - Early Transition Offensive Concepts",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1,thumbnail:"video-2.jpg"},{id:6,title:`WR Fundamentals
PoA - Herman Wiggins`,date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1,thumbnail:"video-3.jpg"},{id:7,title:"Frank Martin - Drills and Coaching Philosophies...",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0,thumbnail:"video-1.jpg"},{id:8,title:"John Calipari - Early Transition Offensive Concepts",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1,thumbnail:"video-2.jpg"}],f=()=>{const[i,n]=u.useState(y),l=p(),o=a=>{n(s=>s.map(t=>t.id===a?{...t,status:!t.status}:t))},r=a=>{l(`/seller/strategy-details/${a}`)},d=[{key:"no",label:"No.",className:"no"},{key:"content",label:"Videos/Documents",render:a=>e.jsxs("div",{className:"video-doc",children:[e.jsx("img",{src:a.thumbnail,alt:"video thumb"}),e.jsx("span",{children:a.title})]})},{key:"date",label:"Date"},{key:"price",label:"Price"},{key:"status",label:"Status",render:a=>e.jsxs("label",{className:"switch",children:[e.jsx("input",{type:"checkbox",checked:a.status,onChange:()=>o(a.id)}),e.jsx("span",{className:"slider round"})]})},{key:"action",label:"Action",render:a=>e.jsx("div",{className:"action-icon-container",children:e.jsx(g,{className:"eyeicon",onClick:()=>r(a.id)})})}],c=a=>a.map((s,t)=>({...s,no:t+1}));return e.jsx(h,{children:e.jsx("div",{className:"video-status-container",children:e.jsx(m,{columns:d,data:c(i),className:"video-table"})})})};export{f as default};
