import{d as v,u as X,e as o,U as E,j as e,V as Z,W as p,J as S,X as $,Y as U,Z as b,$ as z,a0 as u,a1 as H,r as n,a2 as M,f as F,x as K,a3 as Q,a4 as ee,a5 as se,a6 as ae,a7 as re,a8 as A,a9 as ie,aa as te,ab as L,ac as le,ad as de,ae as ne,af as ce,ag as oe,ah as ue,ai as me,aj as I,ak as R,al as _e,am as B,an as ye,o as he}from"./index-DPaefDjm.js";import{S as x,B as T}from"./BuyerAccountDashboard-BmTLAUo-.js";import{t as w}from"./toast-jTgG8ilJ.js";import{T as P}from"./Table-K41WFawU.js";const pe=()=>{const s=v(),r=X(),t=o(E),a=d=>{switch(s(u(d)),d){case"dashboard":r("/buyer/account/dashboard");break;case"profile":r("/buyer/account/profile");break;case"downloads":r("/buyer/account/downloads");break;case"requests":r("/buyer/account/requests");break;case"bids":r("/buyer/account/bids");break;case"cards":r("/buyer/account/cards");break;case"logout":s(H()),r("/");break;default:r("/buyer/account/dashboard")}};return e.jsx("div",{className:"BuyerSidebar",children:e.jsx("div",{className:"BuyerSidebar__container",children:e.jsxs("ul",{className:"BuyerSidebar__menu",children:[e.jsxs("li",{className:`BuyerSidebar__item ${t==="dashboard"?"active":""}`,onClick:()=>a("dashboard"),children:[e.jsx(Z,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"Dashboard"})]}),e.jsxs("li",{className:`BuyerSidebar__item ${t==="profile"?"active":""}`,onClick:()=>a("profile"),children:[e.jsx(p,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"My Profile"})]}),e.jsxs("li",{className:`BuyerSidebar__item ${t==="downloads"?"active":""}`,onClick:()=>a("downloads"),children:[e.jsx(S,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"My Downloads"})]}),e.jsxs("li",{className:`BuyerSidebar__item ${t==="requests"?"active":""}`,onClick:()=>a("requests"),children:[e.jsx($,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"My Requests"})]}),e.jsxs("li",{className:`BuyerSidebar__item ${t==="bids"?"active":""}`,onClick:()=>a("bids"),children:[e.jsx(U,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"My Bids"})]}),e.jsxs("li",{className:`BuyerSidebar__item ${t==="cards"?"active":""}`,onClick:()=>a("cards"),children:[e.jsx(b,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"My Cards"})]}),e.jsxs("li",{className:"BuyerSidebar__item BuyerSidebar__logout",onClick:()=>a("logout"),children:[e.jsx(z,{className:"BuyerSidebar__icon"}),e.jsx("span",{children:"Logout"})]})]})})})},xe=()=>{const s=v(),{user:r,isLoading:t,isSuccess:a,isError:d,error:y}=o(m=>m.auth),[l,j]=n.useState({firstName:"",lastName:"",email:"",mobile:"",profileImage:""}),[D,C]=n.useState(!0),[f,i]=n.useState(null),[k,Y]=n.useState(null);n.useEffect(()=>{s(M())},[s]),n.useEffect(()=>{r&&(j({firstName:r.firstName||"",lastName:r.lastName||"",email:r.email||"",mobile:r.mobile||"",profileImage:r.profileImage||""}),g(!1),C(!1))},[r]);const[h,N]=n.useState(!1),[O,g]=n.useState(!1);n.useEffect(()=>{h&&a&&!t&&(w.success("Profile updated successfully!"),s(F()),N(!1),s(M())),h&&d&&y&&(w.error(y.message||"Failed to update profile"),s(F()),N(!1))},[a,d,y,t,s,h]);const q=m=>{const{name:c,value:_}=m.target;j({...l,[c]:_})},V=async m=>{m.preventDefault(),N(!0);try{let c=l.profileImage;f&&(c=(await s(se(f)).unwrap()).data.fileUrl);const _={firstName:l.firstName,lastName:l.lastName,profileImage:c};s(ae(_))}catch{w.error("Failed to upload image or update profile"),N(!1)}},W=m=>{const c=m.target.files[0];if(c){i(c),g(!1);const _=new FileReader;_.onloadend=()=>{Y(_.result)},_.readAsDataURL(c)}},G=()=>{g(!0)},J=()=>{window.confirm("Are you sure you want to delete your account? This action cannot be undone.")};return e.jsx("div",{className:"BuyerProfile",children:e.jsx(x,{icon:e.jsx(p,{className:"BuyerSidebar__icon"}),title:"My Profile",children:e.jsxs("div",{className:"profile_border_container",children:[e.jsxs("div",{className:"BuyerProfile__container",children:[e.jsxs("div",{className:"BuyerProfile__left-section",children:[e.jsxs("div",{className:"BuyerProfile__form-row",children:[e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(p,{})}),e.jsx("input",{type:"text",id:"firstName",name:"firstName",value:l.firstName,onChange:q,placeholder:"First Name",required:!0,className:"BuyerProfile__input"})]})}),e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(p,{})}),e.jsx("input",{type:"text",id:"lastName",name:"lastName",value:l.lastName,onChange:q,placeholder:"Last Name",required:!0,className:"BuyerProfile__input"})]})})]}),e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(K,{})}),e.jsx("input",{type:"email",id:"email",name:"email",value:l.email,placeholder:"Email Address",className:"BuyerProfile__input BuyerProfile__input--readonly",readOnly:!0,disabled:!0})]})}),e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(Q,{})}),e.jsx("input",{type:"tel",id:"mobile",name:"mobile",value:l.mobile,placeholder:"Mobile Number",className:"BuyerProfile__input BuyerProfile__input--readonly",readOnly:!0,disabled:!0})]})})]}),e.jsx("div",{className:"BuyerProfile__right-section",children:e.jsxs("div",{className:"BuyerProfile__image-container",children:[e.jsx("h3",{className:"BuyerProfile__image-title",children:"Profile Image"}),e.jsx("div",{className:"BuyerProfile__image",children:k||l.profileImage&&!O?e.jsx("img",{src:k||ee(l.profileImage),alt:"Profile",onError:G}):e.jsx("div",{className:"BuyerProfile__placeholder",children:l.firstName&&l.lastName?`${l.firstName.charAt(0)}${l.lastName.charAt(0)}`:e.jsx(p,{className:"BuyerProfile__user-icon"})})}),e.jsx("button",{className:"BuyerProfile__upload-btn",onClick:()=>document.getElementById("profile-image-upload").click(),children:"Upload Photo"}),e.jsx("input",{type:"file",id:"profile-image-upload",accept:"image/*",onChange:W,style:{display:"none"}})]})})]}),e.jsxs("div",{className:"BuyerProfile__buttons mt-30",children:[e.jsx("button",{type:"button",className:"BuyerProfile__delete-btn",onClick:J,children:"Delete Account"}),e.jsx("button",{type:"button",className:"BuyerProfile__save-btn",onClick:V,disabled:h||t,children:h||t?"Updating...":"Update & Save"})]})]})})})},je=()=>{const s=o(re),r=a=>{switch(a.toLowerCase()){case"pdf":return e.jsx(A,{className:"BuyerDownloads__file-icon BuyerDownloads__file-icon--pdf"});case"video":return e.jsx(ie,{className:"BuyerDownloads__file-icon BuyerDownloads__file-icon--video"});default:return e.jsx(A,{className:"BuyerDownloads__file-icon"})}},t=[{key:"fileType",label:"Type",render:a=>r(a.fileType)},{key:"title",label:"Title"},{key:"downloadDate",label:"Download Date"},{key:"action",label:"Action",render:()=>e.jsxs("button",{className:"BuyerDownloads__download-btn",children:[e.jsx(S,{})," Download"]})}];return e.jsx("div",{className:"BuyerDownloads",children:e.jsx(x,{icon:e.jsx(S,{className:"BuyerSidebar__icon"}),title:"My Downloads",children:s.length>0?e.jsx(P,{columns:t,data:s,className:"BuyerDownloads__table",emptyMessage:"You have no downloads yet."}):e.jsx("div",{className:"BuyerDownloads__empty",children:e.jsx("p",{children:"You have no downloads yet."})})})})},fe=()=>{const s=o(te),r=a=>{switch(a){case"pending":return"BuyerRequests__status--pending";case"approved":return"BuyerRequests__status--approved";case"completed":return"BuyerRequests__status--completed";default:return""}},t=[{key:"title",label:"Title"},{key:"description",label:"Description"},{key:"status",label:"Status",render:a=>e.jsx("div",{className:`BuyerRequests__item-status ${r(a.status)}`,children:a.status.charAt(0).toUpperCase()+a.status.slice(1)})},{key:"date",label:"Requested On",render:a=>`Requested on: ${a.date}`}];return e.jsx("div",{className:"BuyerRequests",children:e.jsxs(x,{icon:e.jsx($,{className:"BuyerSidebar__icon"}),title:"My Requests",children:[e.jsx("div",{className:"BuyerRequests__header",children:e.jsxs("button",{className:"BuyerRequests__add-btn",children:[e.jsx(L,{})," New Request"]})}),s.length>0?e.jsx(P,{columns:t,data:s,className:"BuyerRequests__table",emptyMessage:"You have no requests yet."}):e.jsx("div",{className:"BuyerRequests__empty",children:e.jsx("p",{children:"You have no requests yet."})})]})})},Ne=()=>{const s=o(le),r=a=>{switch(a){case"active":return"BuyerBids__status--active";case"won":return"BuyerBids__status--won";case"lost":return"BuyerBids__status--lost";default:return""}},t=[{key:"title",label:"Title"},{key:"coach",label:"Coach"},{key:"bidAmount",label:"Bid Amount",render:a=>`$${a.bidAmount.toFixed(2)}`},{key:"date",label:"Date"},{key:"status",label:"Status",render:a=>e.jsx("div",{className:`BuyerBids__item-status ${r(a.status)}`,children:a.status.charAt(0).toUpperCase()+a.status.slice(1)})}];return e.jsx("div",{className:"BuyerBids",children:e.jsx(x,{icon:e.jsx(U,{className:"BuyerSidebar__icon"}),title:"My Bids",children:s.length>0?e.jsx(P,{columns:t,data:s,className:"BuyerBids__table",emptyMessage:"You have no bids yet."}):e.jsx("div",{className:"BuyerBids__empty",children:e.jsx("p",{children:"You have no bids yet."})})})})},Be=()=>{const s=v(),r=o(de),t=o(ne),a=o(ce),d=()=>{s(I(t==="list"?"add":"list")),t==="add"&&s(R())},y=i=>{window.confirm("Are you sure you want to remove this card?")&&s(_e(i))},l=i=>{s(B({nameOnCard:i}))},j=i=>{s(B({cardNumber:i}))},D=i=>{s(B({expiryDate:i}))},C=i=>{s(B({cvv:i}))},f=()=>{const i={id:Date.now().toString(),lastFourDigits:a.cardNumber.slice(-4),cardType:"mastercard"};s(ye(i)),s(R()),s(I("list"))};return e.jsx("div",{className:"BuyerCards",children:e.jsx(x,{icon:e.jsx(b,{className:"BuyerSidebar__icon"}),title:"My Cards",children:e.jsx("div",{className:"buyercardsbordercontainer",children:t==="list"?e.jsxs("div",{className:"BuyerCards__list-view",children:[e.jsxs("div",{className:"BuyerCards__header",children:[e.jsx("h3",{className:"BuyerCards__subtitle",children:"Saved Cards"}),e.jsxs("button",{className:"BuyerCards__add-btn",onClick:d,children:[e.jsx(L,{})," Add New Card"]})]}),e.jsx("div",{className:"BuyerCards__cards-list",children:r.length>0?r.map(i=>e.jsxs("div",{className:"BuyerCards__card-item",children:[e.jsxs("div",{className:"BuyerCards__card-content",children:[e.jsx("div",{className:"BuyerCards__card-logo",children:e.jsx("img",{src:"https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/200px-Mastercard-logo.svg.png",alt:"Mastercard"})}),e.jsxs("div",{className:"BuyerCards__card-number",children:["•••• •••• •••• ",i.lastFourDigits]})]}),e.jsx("button",{className:"BuyerCards__delete-btn",onClick:()=>y(i.id),"aria-label":"Delete card",children:e.jsx(oe,{})})]},i.id)):e.jsx("div",{className:"BuyerCards__empty-state",children:e.jsx("p",{children:"You have no saved payment methods yet."})})})]}):e.jsxs("div",{className:"BuyerCards__add-view",children:[e.jsx("div",{className:"BuyerCards__header",children:e.jsx("h3",{className:"BuyerCards__subtitle",children:"Add New Card"})}),e.jsx("div",{className:"BuyerCards__form",children:e.jsxs("form",{onSubmit:i=>{i.preventDefault(),f()},children:[e.jsx("div",{className:"BuyerCards__form-row",children:e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--full",children:e.jsxs("div",{className:"BuyerCards__input-container",children:[e.jsx("div",{className:"BuyerCards__input-icon",children:e.jsx(b,{})}),e.jsx("input",{type:"text",id:"nameOnCard",name:"nameOnCard",value:a.nameOnCard,onChange:i=>l(i.target.value),placeholder:"Name on card",required:!0,className:"BuyerCards__input"})]})})}),e.jsxs("div",{className:"BuyerCards__form-row",children:[e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--card-number",children:e.jsxs("div",{className:"BuyerCards__input-container",children:[e.jsx("div",{className:"BuyerCards__input-icon",children:e.jsx(b,{})}),e.jsx("input",{type:"text",id:"cardNumber",name:"cardNumber",value:a.cardNumber,onChange:i=>j(i.target.value),placeholder:"Card Number",required:!0,maxLength:19,pattern:"[0-9\\\\s]{13,19}",className:"BuyerCards__input"})]})}),e.jsx("div",{className:"BuyerCards__card-logo",children:e.jsx("img",{src:"https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/200px-Mastercard-logo.svg.png",alt:"Mastercard"})})]}),e.jsxs("div",{className:"BuyerCards__form-row",children:[e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--half",children:e.jsxs("div",{className:"BuyerCards__input-container",children:[e.jsx("div",{className:"BuyerCards__input-icon",children:e.jsx(ue,{})}),e.jsx("input",{type:"text",id:"expiryDate",name:"expiryDate",value:a.expiryDate,onChange:i=>D(i.target.value),placeholder:"MM/YY",required:!0,maxLength:5,pattern:"^(0[1-9]|1[0-2])\\/([0-9]{2})$",className:"BuyerCards__input"})]})}),e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--half",children:e.jsxs("div",{className:"BuyerCards__input-container",children:[e.jsx("div",{className:"BuyerCards__input-icon",children:e.jsx(me,{})}),e.jsx("input",{type:"text",id:"cvv",name:"cvv",value:a.cvv,onChange:i=>C(i.target.value.replace(/\D/g,"")),placeholder:"CVV",required:!0,maxLength:4,pattern:"[0-9]{3,4}",className:"BuyerCards__input"})]})})]}),e.jsx("div",{className:"BuyerCards__form-actions",children:e.jsx("button",{type:"submit",className:"BuyerCards__submit-btn",children:"Add Card"})})]})}),e.jsx("div",{className:"BuyerCards__form-actions",children:e.jsx("button",{className:"BuyerCards__cancel-btn",onClick:d,children:"Cancel"})})]})})})})},Se=()=>{const s=v(),r=he(),t=o(E);n.useEffect(()=>{const d=r.pathname;d.includes("/dashboard")?s(u("dashboard")):d.includes("/profile")?s(u("profile")):d.includes("/downloads")?s(u("downloads")):d.includes("/requests")?s(u("requests")):d.includes("/bids")?s(u("bids")):d.includes("/cards")?s(u("cards")):s(u("dashboard"))},[r.pathname,s]);const a=()=>{switch(t){case"dashboard":return e.jsx(T,{});case"profile":return e.jsx(xe,{});case"downloads":return e.jsx(je,{});case"requests":return e.jsx(fe,{});case"bids":return e.jsx(Ne,{});case"cards":return e.jsx(Be,{});default:return e.jsx(T,{})}};return e.jsx("div",{className:"BuyerAccount",children:e.jsxs("div",{className:"container max-container",children:[e.jsx("div",{className:"sidebar",children:e.jsx(pe,{})}),e.jsx("div",{className:"content",children:a()})]})})};export{Se as default};
