import{j as s,P as l,L as n}from"./index-DPaefDjm.js";const m=({image:e,title:a,coach:r,price:c,hasVideo:t,id:d,type:i="buy"})=>s.jsxs("div",{className:"strategy-card-component strategy-card",children:[s.jsxs("div",{className:"strategy-card-image",children:[s.jsx("img",{src:e,alt:a}),t&&s.jsx("div",{className:"video-icon",children:s.jsx(l,{})})]}),s.jsxs("div",{className:"strategy-card-content",children:[s.jsx("h3",{className:"strategy-card-title",children:a}),s.jsxs("p",{className:"strategy-card-coach",children:["By ",r]}),s.jsxs("div",{className:"strategy-card-footer",children:[s.jsxs("span",{className:"strategy-card-price",children:["$",c.toFixed(2)]}),s.jsx(n,{to:`/buyer/details/${d}`,className:"action-button ",children:i==="bid"?"Bid Now":"Buy Now"})]})]})]});export{m as S};
