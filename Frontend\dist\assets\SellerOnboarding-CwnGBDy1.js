import{r as x,j as e,ag as R,d as $,u as q,e as B,aO as D,a2 as Z,aP as _,a5 as G,aQ as W}from"./index-DPaefDjm.js";import{s as z,a as O}from"./toast-jTgG8ilJ.js";const X=({formData:m,onInputChange:P,onExperienceChange:v,onAddExperience:U,onRemoveExperience:I,onNext:h,fieldErrors:a})=>{const[Y,y]=x.useState(m.profilePic||null),[i,N]=x.useState(""),g=["jpg","jpeg","png"],u=10*1024*1024;x.useEffect(()=>{m.profilePic&&m.profilePic!==Y&&y(m.profilePic)},[m.profilePic,Y]);const T=c=>{if(c.size>u)return"File size must be less than 10MB";const n=c.name.split(".").pop().toLowerCase();return g.includes(n)?["image/jpeg","image/jpg","image/png","image/gif"].includes(c.type)?null:"Invalid file type. Please select a valid image file.":`Unsupported file format. Please use: ${g.join(", ").toUpperCase()}`},F=c=>{const n=c.target.files[0];if(n){N("");const f=T(n);if(f){N(f),c.target.value="";return}const b=new FileReader;b.onload=S=>{y(S.target.result)},b.readAsDataURL(n),P("selectedImageFile",n),console.log("Image selected for later upload:",n.name)}};return e.jsxs("div",{className:"seller-onboarding-step1-container max-container",children:[e.jsxs("div",{className:"progress-bar",children:[e.jsx("div",{className:"step active",children:"1"}),e.jsx("div",{className:"progress-line"}),e.jsx("div",{className:"step",children:"2"})]}),e.jsxs("div",{className:"form-grid",children:[e.jsxs("div",{className:"description-section",children:[e.jsx("div",{className:"section-title",children:"Description"}),e.jsxs("div",{className:"description-box",children:[e.jsx("textarea",{className:`description-textarea ${a!=null&&a.description?"error":""}`,placeholder:"Write Description..",rows:3,value:m.description,onChange:c=>P("description",c.target.value)}),(a==null?void 0:a.description)&&e.jsx("div",{className:"field-error",children:a.description})]})]}),e.jsxs("div",{className:"profile-experience-grid",children:[e.jsxs("div",{className:"profile-pic-section",children:[e.jsx("div",{className:"section-title",children:"Profile Pic"}),e.jsxs("div",{className:"avatar-upload",children:[e.jsx("div",{className:"avatar-placeholder",children:Y||m.profilePic?e.jsx("img",{src:Y||m.profilePic,alt:"Profile",className:"avatar-image"}):e.jsxs("svg",{width:"64",height:"64",viewBox:"0 0 64 64",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("circle",{cx:"32",cy:"32",r:"32",fill:"var(--light-gray)"}),e.jsx("ellipse",{cx:"32",cy:"27",rx:"12",ry:"12",fill:"#fff"}),e.jsx("ellipse",{cx:"32",cy:"50",rx:"16",ry:"10",fill:"#fff"})]})}),e.jsx("input",{type:"file",id:"profilePicInput",accept:"image/jpeg,image/jpg,image/png,image/gif",onChange:F,style:{display:"none"}}),e.jsx("div",{className:"upload-buttons",children:e.jsx("button",{type:"button",className:"btn btn-outline upload-btn",onClick:()=>document.getElementById("profilePicInput").click(),children:"Choose Photo"})}),e.jsx("div",{className:"upload-info",children:e.jsxs("small",{className:"upload-format-info",children:["Supported formats: ",g.join(", ").toUpperCase()," (Max: 10MB)"]})}),i&&e.jsx("div",{className:"field-error image-error",children:i})]})]}),e.jsxs("div",{className:"experience-section",children:[e.jsx("div",{className:"section-title",children:"Experience"}),e.jsxs("div",{className:"experience-container",children:[m.experiences.map((c,n)=>{var f,b,S,M,w,V,A,L;return e.jsxs("div",{className:"experience-row",children:[e.jsxs("div",{className:"experience-row-content",children:[e.jsx("input",{type:"text",className:`input ${a!=null&&a.experiences&&n===0?"error":""}`,placeholder:"Enter School Name",value:c.schoolName,onChange:j=>v(n,"schoolName",j.target.value)}),e.jsx("input",{type:"text",className:"input",placeholder:"Enter Position",value:c.position,onChange:j=>v(n,"position",j.target.value)}),e.jsxs("div",{className:"year-fields",children:[e.jsxs("div",{children:[e.jsx("input",{type:"text",className:`input year-input ${(b=(f=a==null?void 0:a.experienceYears)==null?void 0:f[n])!=null&&b.fromYear?"error":""}`,placeholder:"From Year",value:c.fromYear,onChange:j=>v(n,"fromYear",j.target.value)}),((M=(S=a==null?void 0:a.experienceYears)==null?void 0:S[n])==null?void 0:M.fromYear)&&e.jsx("div",{className:"field-error",children:a.experienceYears[n].fromYear})]}),e.jsxs("div",{children:[e.jsx("input",{type:"text",className:`input year-input ${(V=(w=a==null?void 0:a.experienceYears)==null?void 0:w[n])!=null&&V.toYear?"error":""}`,placeholder:"To Year",value:c.toYear,onChange:j=>v(n,"toYear",j.target.value)}),((L=(A=a==null?void 0:a.experienceYears)==null?void 0:A[n])==null?void 0:L.toYear)&&e.jsx("div",{className:"field-error",children:a.experienceYears[n].toYear})]})]})]}),m.experiences.length>1&&I&&e.jsx("button",{type:"button",className:"delete-experience-btn",onClick:()=>I(n),title:"Remove this experience",children:e.jsx(R,{})})]},n)}),(a==null?void 0:a.experiences)&&e.jsx("div",{className:"field-error",children:a.experiences}),e.jsx("div",{className:"add-more-link",onClick:U,children:"+ Add More"})]})]})]})]}),e.jsx("div",{className:"next-btn-row",children:e.jsx("button",{className:"btn btn-primary next-btn",onClick:h,children:"Next"})})]})},ee=()=>{const m=$(),P=q(),{isLoading:v,isSuccess:U,isError:I,error:h,onboardingData:a}=B(s=>s.user),[Y,y]=x.useState(1),[i,N]=x.useState({description:"",profilePic:"",selectedImageFile:null,experiences:[{schoolName:"",position:"",fromYear:"",toYear:""}],minTrainingCost:"",socialLinks:{facebook:"",linkedin:"",twitter:""},sports:[],expertise:[],certifications:[]}),[g,u]=x.useState({description:"",experiences:"",minTrainingCost:"",experienceYears:{},profileImage:""}),[T,F]=x.useState(""),[c,n]=x.useState({isUploadingImage:!1,isSubmittingForm:!1,uploadProgress:""});x.useEffect(()=>{document.body.style.pointerEvents="none";const s=document.querySelector(".seller-onboarding-wrapper");return s&&(s.style.pointerEvents="all"),()=>{document.body.style.pointerEvents="all"}},[]),x.useEffect(()=>{if(U&&(n({isUploadingImage:!1,isSubmittingForm:!1,uploadProgress:""}),F(""),z("Onboarding completed successfully! Welcome to XO Sports Hub!",{autoClose:4e3}),a&&m(D(a)),m(Z()).then(()=>{m(_()),setTimeout(()=>{P("/seller/dashboard")},1500)})),I&&h){n({isUploadingImage:!1,isSubmittingForm:!1,uploadProgress:""});let s="An error occurred during onboarding. Please try again.";h.message?s=h.message:h.errors&&Array.isArray(h.errors)&&(s=h.errors.map(o=>o.msg).join(", ")),F(s),O(s)}},[U,I,h,m,P,a]);const f=(s,t)=>{console.log(`Updating ${s}:`,t),N(o=>({...o,[s]:t})),g[s]&&u(o=>({...o,[s]:""}))},b=()=>{const s=new Date().getFullYear(),t=1950,o=s,p={};let r=!1;return i.experiences.forEach((l,d)=>{const k=parseInt(l.fromYear),H=parseInt(l.toYear),C={};String(l.fromYear||"").trim()?(isNaN(k)||k<t||k>o)&&(C.fromYear=`From year must be between ${t} and ${o}`,r=!0):(C.fromYear="From year is required",r=!0),String(l.toYear||"").trim()?isNaN(H)||H<t||H>o?(C.toYear=`To year must be between ${t} and ${o}`,r=!0):!isNaN(k)&&H<k&&(C.toYear="To year must be greater than or equal to from year",r=!0):(C.toYear="To year is required",r=!0),Object.keys(C).length>0&&(p[d]=C)}),r?(u(l=>({...l,experienceYears:p})),!1):(u(l=>({...l,experienceYears:{}})),!0)},S=()=>{const s={};let t=!1;return i.description.trim()||(s.description="Description is required",t=!0),(i.experiences.length===0||!i.experiences[0].schoolName)&&(s.experiences="At least one experience with school name is required",t=!0),b()||(t=!0),t?(u(p=>({...p,...s})),!1):(u({description:"",experiences:"",minTrainingCost:"",experienceYears:{},profileImage:""}),!0)},M=()=>{S()&&y(2)};x.useEffect(()=>{console.log("Current formData.profilePic:",i.profilePic)},[i.profilePic]);const w=(s,t)=>{N(o=>({...o,socialLinks:{...o.socialLinks,[s]:t}}))},V=(s,t,o)=>{var r,l;const p=[...i.experiences];p[s]={...p[s],[t]:o},N(d=>({...d,experiences:p})),(t==="fromYear"||t==="toYear")&&(l=(r=g.experienceYears)==null?void 0:r[s])!=null&&l[t]&&u(d=>({...d,experienceYears:{...d.experienceYears,[s]:{...d.experienceYears[s],[t]:""}}}))},A=()=>{N(s=>({...s,experiences:[...s.experiences,{schoolName:"",position:"",fromYear:"",toYear:""}]}))},L=s=>{var t;i.experiences.length>1&&(N(o=>({...o,experiences:o.experiences.filter((p,r)=>r!==s)})),(t=g.experienceYears)!=null&&t[s]&&u(o=>{const p={...o.experienceYears};delete p[s];const r={};return Object.keys(p).forEach(l=>{const d=parseInt(l);d>s?r[d-1]=p[l]:d<s&&(r[d]=p[l])}),{...o,experienceYears:r}}))},j=async()=>{u({description:"",experiences:"",minTrainingCost:"",experienceYears:{}}),F("");const s={};let t=!1;if(i.minTrainingCost||(s.minTrainingCost="Minimum training cost is required",t=!0),i.description.trim()||(s.description="Description is required",t=!0),(i.experiences.length===0||!i.experiences[0].schoolName)&&(s.experiences="At least one experience with school name is required",t=!0),t){u(s),(s.description||s.experiences)&&y(1);return}try{let o=i.profilePic;if(i.selectedImageFile){n(r=>({...r,isUploadingImage:!0,uploadProgress:"Uploading profile image..."})),console.log("Uploading selected image:",i.selectedImageFile.name);try{o=(await m(G(i.selectedImageFile)).unwrap()).data.fileUrl,console.log("Image uploaded successfully:",o),n(l=>({...l,isUploadingImage:!1,uploadProgress:"Image uploaded successfully!"}))}catch(r){console.error("Image upload failed:",r),n(d=>({...d,isUploadingImage:!1,uploadProgress:""}));let l="Failed to upload profile image. Please try again.";r.message?l=r.message:r.errors&&Array.isArray(r.errors)&&(l=r.errors.map(d=>d.msg).join(", ")),u(d=>({...d,profileImage:l})),O(l);return}}n(r=>({...r,isSubmittingForm:!0,uploadProgress:"Submitting onboarding data..."}));const p={description:i.description,profilePic:o,experiences:i.experiences.map(r=>({schoolName:r.schoolName,position:r.position,fromYear:parseInt(r.fromYear)||new Date().getFullYear(),toYear:parseInt(r.toYear)||new Date().getFullYear()})),minTrainingCost:parseFloat(i.minTrainingCost),socialLinks:i.socialLinks,sports:i.sports.length>0?i.sports:["General Sports"],expertise:i.expertise.length>0?i.expertise:["General Training"],certifications:i.certifications};console.log("Submitting onboarding data:",p),m(W(p))}catch(o){console.error("Submission process failed:",o),n(p=>({...p,isUploadingImage:!1,isSubmittingForm:!1,uploadProgress:""})),O("An error occurred during submission. Please try again.")}};return e.jsx("div",{className:"seller-onboarding-wrapper max-container",children:Y===1?e.jsx(X,{formData:i,onInputChange:f,onExperienceChange:V,onAddExperience:A,onRemoveExperience:L,onNext:M,fieldErrors:g}):e.jsxs("div",{className:"seller-onboarding-step2-container",children:[e.jsxs("div",{className:"progress-bar",children:[e.jsx("div",{className:"step complete",children:"1"}),e.jsx("div",{className:"progress-line"}),e.jsx("div",{className:"step active",children:"2"})]}),e.jsxs("div",{className:"section-block",children:[e.jsx("div",{className:"section-title",children:"Minimum Customer Training Cost"}),e.jsx("input",{type:"number",className:`input min-cost-input ${g.minTrainingCost?"error":""}`,placeholder:"Enter amount",value:i.minTrainingCost,onChange:s=>f("minTrainingCost",s.target.value)}),g.minTrainingCost&&e.jsx("div",{className:"field-error",children:g.minTrainingCost})]}),e.jsxs("div",{className:"section-block",children:[e.jsx("div",{className:"section-title",children:"Social Media Account"}),e.jsxs("div",{className:"social-inputs-grid",children:[e.jsxs("div",{className:"social-input-row",children:[e.jsx("span",{className:"social-icon facebook",children:e.jsxs("svg",{width:"20",height:"20",fill:"none",viewBox:"0 0 20 20",children:[e.jsx("path",{d:"M18 10A8 8 0 1 0 10 18V12.89H8.1V10.89H10V9.22C10 7.5 11.17 6.5 12.72 6.5C13.44 6.5 14.2 6.62 14.2 6.62V8.6H13.23C12.27 8.6 12 9.18 12 9.77V10.89H14.1L13.8 12.89H12V18A8 8 0 0 0 18 10Z",fill:"#1877F3"}),e.jsx("path",{d:"M13.8 12.89L14.1 10.89H12V9.77C12 9.18 12.27 8.6 13.23 8.6H14.2V6.62S13.44 6.5 12.72 6.5C11.17 6.5 10 7.5 10 9.22V10.89H8.1V12.89H10V18C10.67 18 11.32 17.93 11.95 17.8V12.89H13.8Z",fill:"#fff"})]})}),e.jsx("input",{type:"text",className:"input social-input",placeholder:"Facebook URL",value:i.socialLinks.facebook,onChange:s=>w("facebook",s.target.value)})]}),e.jsxs("div",{className:"social-input-row",children:[e.jsx("span",{className:"social-icon linkedin",children:e.jsxs("svg",{width:"20",height:"20",fill:"none",viewBox:"0 0 20 20",children:[e.jsx("circle",{cx:"10",cy:"10",r:"10",fill:"#0A66C2"}),e.jsx("path",{d:"M6.94 8.5H4.98V15H6.94V8.5ZM5.96 7.5C6.6 7.5 7.1 7 7.1 6.36C7.1 5.72 6.6 5.22 5.96 5.22C5.32 5.22 4.82 5.72 4.82 6.36C4.82 7 5.32 7.5 5.96 7.5ZM15 15H13.04V11.5C13.04 10.67 12.37 10 11.54 10C10.71 10 10.04 10.67 10.04 11.5V15H8.08V8.5H10.04V9.38C10.41 8.81 11.13 8.5 11.54 8.5C13.01 8.5 15 9.44 15 11.5V15Z",fill:"#fff"})]})}),e.jsx("input",{type:"text",className:"input social-input",placeholder:"LinkedIn URL",value:i.socialLinks.linkedin,onChange:s=>w("linkedin",s.target.value)})]}),e.jsxs("div",{className:"social-input-row",children:[e.jsx("span",{className:"social-icon twitter",children:e.jsxs("svg",{width:"20",height:"20",fill:"none",viewBox:"0 0 20 20",children:[e.jsx("circle",{cx:"10",cy:"10",r:"10",fill:"#1DA1F2"}),e.jsx("path",{d:"M15.32 8.13C15.33 8.23 15.33 8.33 15.33 8.43C15.33 11.13 13.29 14.13 9.5 14.13C8.37 14.13 7.31 13.8 6.4 13.23C6.56 13.25 6.72 13.26 6.89 13.26C7.82 13.26 8.66 12.95 9.36 12.44C8.48 12.43 7.74 11.87 7.49 11.07C7.62 11.09 7.75 11.11 7.89 11.11C8.08 11.11 8.27 11.08 8.45 11.03C7.54 10.85 6.85 10.03 6.85 9.06V9.04C7.11 9.19 7.42 9.28 7.75 9.29C7.19 8.91 6.81 8.28 6.81 7.57C6.81 7.23 6.9 6.92 7.07 6.66C8.04 7.84 9.47 8.62 11.07 8.7C11.04 8.56 11.03 8.41 11.03 8.27C11.03 7.29 11.82 6.5 12.8 6.5C13.29 6.5 13.73 6.7 14.03 7.04C14.4 6.97 14.75 6.85 15.06 6.68C14.95 7.06 14.7 7.37 14.37 7.56C14.7 7.53 15.01 7.44 15.32 7.3C15.06 7.62 14.72 7.89 14.34 8.13H15.32Z",fill:"#fff"})]})}),e.jsx("input",{type:"text",className:"input social-input",placeholder:"Twitter URL",value:i.socialLinks.twitter,onChange:s=>w("twitter",s.target.value)})]})]})]}),T&&e.jsxs("div",{className:"server-error-message",children:[e.jsx("div",{className:"error-icon",children:"⚠️"}),e.jsx("div",{className:"error-text",children:T})]}),e.jsxs("div",{className:"next-btn-row",children:[e.jsx("button",{className:"btn btn-outline",onClick:()=>y(1),disabled:c.isUploadingImage||c.isSubmittingForm||v,children:"Back"}),e.jsx("button",{className:"btn btn-primary next-btn",onClick:j,disabled:c.isUploadingImage||c.isSubmittingForm||v,children:c.isUploadingImage||c.isSubmittingForm||v?c.uploadProgress||"Processing...":"Complete Onboarding"})]})]})})};export{ee as default};
