import{as as n,e as c,ay as o,j as i}from"./index-DPaefDjm.js";import{S as l}from"./SellerLayout-k6HMxGdv.js";import{T as r}from"./Table-K41WFawU.js";const m=(s,a)=>{const e=a.find(t=>t.id===`#${s}`);return e?{...e,time:"4:50PM",customer:e.customer||{name:e.requestedCustomer||"<PERSON>",email:"<EMAIL>",phone:"************"},history:[{bidId:e.id,customer:e.requestedCustomer||"<PERSON>",date:e.date,price:e.price,bidAmount:e.bidAmount,status:"Pending",action:"View"},{bidId:"#BID1234",customer:"<PERSON>",date:"20 May 2024",price:"$25.00",bidAmount:"$22.00",status:"Accepted",action:"View"},{bidId:"#BID1235",customer:"<PERSON>",date:"19 May 2024",price:"$25.00",bidAmount:"$20.00",status:"Rejected",action:"View"},{bidId:"#BID1236",customer:"<PERSON> <PERSON>",date:"18 May 2024",price:"$25.00",bidAmount:"$23.00",status:"Counter Offer",action:"View"},{bidId:"#BID1237",customer:"<PERSON> <PERSON>",date:"17 May 2024",price:"$25.00",bidAmount:"$21.00",status:"Pending",action:"View"},{bidId:"#BID1238",customer:"Lisa Davis",date:"16 May 2024",price:"$25.00",bidAmount:"$24.00",status:"Accepted",action:"View"}]}:{id:`#${s}`,title:"Frank Martin - Drills And Coaching Philosophies To Developing Toughness In Your Players",subtitle:"Basketball Coaching Clinic",image:"https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",date:"21 May 2024",time:"4:50PM",price:"$25.00",bidAmount:"$22.00",customer:{name:"John Smith",email:"<EMAIL>",phone:"************"},history:[{bidId:`#${s}`,customer:"John Smith",date:"21 May 2024",price:"$25.00",bidAmount:"$22.00",status:"Pending",action:"View"},{bidId:"#BID1234",customer:"Jane Doe",date:"20 May 2024",price:"$25.00",bidAmount:"$22.00",status:"Accepted",action:"View"},{bidId:"#BID1235",customer:"Mike Johnson",date:"19 May 2024",price:"$25.00",bidAmount:"$20.00",status:"Rejected",action:"View"},{bidId:"#BID1236",customer:"Sarah Wilson",date:"18 May 2024",price:"$25.00",bidAmount:"$23.00",status:"Counter Offer",action:"View"},{bidId:"#BID1237",customer:"David Brown",date:"17 May 2024",price:"$25.00",bidAmount:"$21.00",status:"Pending",action:"View"},{bidId:"#BID1238",customer:"Lisa Davis",date:"16 May 2024",price:"$25.00",bidAmount:"$24.00",status:"Accepted",action:"View"}]}},D=()=>{const{id:s}=n(),a=c(o),e=m(s,a),t=[{key:"bidId",label:"Bid Id"},{key:"customer",label:"Customer"},{key:"date",label:"Date"},{key:"price",label:"Price"},{key:"bidAmount",label:"Bid Amount"},{key:"status",label:"Status",render:d=>i.jsx("span",{className:`status-badge status-${d.status.toLowerCase().replace(" ","-")}`,children:d.status})},{key:"action",label:"Action"}];return e?i.jsx(l,{children:i.jsx("div",{className:"BidDetails",children:i.jsxs("div",{className:"BidDetails__content",children:[i.jsxs("div",{className:"BidDetails__main-section",children:[i.jsx("div",{className:"BidDetails__header",children:i.jsxs("div",{className:"BidDetails__content-info",children:[i.jsx("img",{src:e.image,alt:e.title,className:"BidDetails__content-image"}),i.jsxs("div",{className:"BidDetails__content-details",children:[i.jsx("h3",{className:"BidDetails__content-title",children:e.title}),i.jsx("p",{className:"BidDetails__content-subtitle",children:e.subtitle})]})]})}),i.jsx("div",{className:"BidDetails__info-grid",children:i.jsxs("div",{className:"BidDetails__info-section",children:[i.jsxs("div",{className:"BidDetails__info-item-grid",children:[i.jsx("h3",{className:"BidDetails__section-title",children:"Bid Information"}),i.jsxs("div",{children:[i.jsxs("div",{className:"BidDetails__info-item",children:[i.jsx("span",{className:"BidDetails__info-label",children:"Bid Id"}),i.jsx("span",{className:"BidDetails__info-value",children:e.id})]}),i.jsxs("div",{className:"BidDetails__info-item",children:[i.jsx("span",{className:"BidDetails__info-label",children:"Date"}),i.jsxs("span",{className:"BidDetails__info-value",children:[e.date," | ",e.time]})]}),i.jsx("div",{className:"vertical-line"})]}),i.jsxs("div",{children:[i.jsxs("div",{className:"BidDetails__info-item",children:[i.jsx("span",{className:"BidDetails__info-label",children:"Price"}),i.jsx("span",{className:"BidDetails__info-value",children:e.price})]}),i.jsxs("div",{className:"BidDetails__info-item",children:[i.jsx("span",{className:"BidDetails__info-label",children:"Bid Amount"}),i.jsx("span",{className:"BidDetails__info-value",children:e.bidAmount})]})]})]}),i.jsx("div",{className:"vertical-line"}),i.jsxs("div",{className:"BidDetails__info-item-grid",children:[i.jsx("h3",{className:"BidDetails__section-title",children:"Customer Details"}),i.jsxs("div",{className:"BidDetails__info-item",children:[i.jsx("span",{className:"BidDetails__info-label",children:"Name"}),i.jsx("span",{className:"BidDetails__info-value",children:e.customer.name})]}),i.jsxs("div",{className:"BidDetails__info-item",children:[i.jsx("span",{className:"BidDetails__info-label",children:"Email Address"}),i.jsx("span",{className:"BidDetails__info-value",children:e.customer.email})]}),i.jsxs("div",{className:"BidDetails__info-item",children:[i.jsx("span",{className:"BidDetails__info-label",children:"Phone Number"}),i.jsx("span",{className:"BidDetails__info-value",children:e.customer.phone})]})]})]})})]}),i.jsxs("div",{className:"BidDetails__actions",children:[i.jsx("button",{className:"BidDetails__btn BidDetails__btn--accept",children:"Accepted"}),i.jsx("button",{className:"BidDetails__btn btn-primary",children:"Rejected"})]}),i.jsxs("div",{className:"BidDetails__history-section",children:[i.jsx("h3",{className:"BidDetails__section-title",children:"History"}),i.jsx(r,{columns:t,data:e.history,className:"BidDetails__history-table"})]})]})})}):i.jsx(l,{children:i.jsx("div",{className:"BidDetails",children:i.jsx("div",{className:"BidDetails__error",children:i.jsx("p",{children:"Bid not found"})})})})};export{D as default};
