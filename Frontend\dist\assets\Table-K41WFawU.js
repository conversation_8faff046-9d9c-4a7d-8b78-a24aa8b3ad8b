import{j as a}from"./index-DPaefDjm.js";const b=({columns:l,data:r,onRowClick:h,renderRow:c,className:x="",emptyMessage:i="No data available",variant:t="default"})=>{const j=e=>{h&&h(e)};return a.jsx("div",{className:`table-container ${t} ${x}`,children:t==="grid"?a.jsxs("div",{className:"table",children:[a.jsx("div",{className:"table-header",children:l.map(e=>a.jsx("div",{className:`table-cell ${e.className||""}`,children:e.label},e.key))}),r.length>0?r.map((e,d)=>a.jsx("div",{className:"table-row",onClick:()=>j(e),children:c?c(e,d):l.map(s=>a.jsx("div",{className:`table-cell ${s.className||""}`,children:s.render?s.render(e):e[s.key]},s.key))},e.id||d)):a.jsx("div",{className:"table-row",children:a.jsx("div",{className:"table-cell empty-message",colSpan:l.length,children:i})})]}):a.jsxs("table",{className:"table",children:[a.jsx("thead",{children:a.jsx("tr",{children:l.map(e=>a.jsx("th",{className:e.className||"",children:e.label},e.key))})}),a.jsx("tbody",{children:r.length>0?r.map((e,d)=>a.jsx("tr",{onClick:()=>j(e),className:h?"clickable":"",children:c?c(e,d):l.map(s=>a.jsx("td",{className:s.className||"",children:s.render?s.render(e):e[s.key]},s.key))},e.id||d)):a.jsx("tr",{children:a.jsx("td",{colSpan:l.length,className:"empty-message",children:i})})})]})})};export{b as T};
