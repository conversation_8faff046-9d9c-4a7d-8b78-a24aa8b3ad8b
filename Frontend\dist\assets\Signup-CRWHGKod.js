import{u as S,d as C,e as w,r as _,j as e,b as x,a as F,c as $,i as N,M as E,L as j,f as v,k as q,g as D,h as I,m as G}from"./index-DPaefDjm.js";import{H as P,G as A,f as y}from"./index-ztncCved.js";import{t as l}from"./toast-jTgG8ilJ.js";const B=()=>{const o=S(),c=C(),{isLoading:d,isError:L,isSuccess:M,error:O}=w(s=>s.auth),[n,m]=_.useState({firstName:"",lastName:"",email:"",phone:"",countryCode:"+91",accountType:"learn",agreeToTerms:!1}),[i,h]=_.useState({}),u=s=>{const{name:a,value:r,type:t,checked:p}=s.target;m({...n,[a]:t==="checkbox"?p:r}),i[a]&&h({...i,[a]:null})},g=s=>{m({...n,accountType:s})},f=s=>{m({...n,countryCode:s.target.value})},b=()=>{const s={};return n.firstName.trim()||(s.firstName="First name is required"),n.lastName.trim()||(s.lastName="Last name is required"),n.email.trim()?/\S+@\S+\.\S+/.test(n.email)||(s.email="Email is invalid"):s.email="Email is required",n.phone.trim()?/^\d{10}$/.test(n.phone)||(s.phone="Phone number must be 10 digits"):s.phone="Phone number is required",n.agreeToTerms||(s.agreeToTerms="You must agree to the terms and conditions"),s},T=async s=>{s.preventDefault();const a=b();if(Object.keys(a).length>0){h(a);return}c(v());try{const r={firstName:n.firstName,lastName:n.lastName,email:n.email,mobile:`${n.countryCode}${n.phone}`,role:n.accountType==="learn"?"buyer":"seller"},t=await c(q(r)).unwrap();l.auth.registrationSuccess(),o("/otp-verification",{state:{userId:t.userId,phoneNumber:`${n.countryCode} ${n.phone}`,cooldownSeconds:t.cooldownSeconds||60,isLogin:!1,developmentOtp:t.developmentOtp}})}catch(r){r.includes("already registered")?l.error("This email or mobile number is already registered. Please try logging in instead."):l.api.error({response:{data:{message:r}}})}},k=async()=>{try{if(c(v()),!y.isInitialized()){l.error("Firebase is not initialized. Please check your configuration.");return}const s=await y.signInWithGoogle();try{const a=await c(D(s.idToken)).unwrap();if(l.info("Account already exists. Redirecting to dashboard..."),a.user.role==="buyer")o("/buyer/dashboard");else if(a.user.role==="seller"){const r=I(a.user);o(r)}else a.user.role==="admin"?o("/admin/dashboard"):o("/")}catch(a){const r=typeof a=="string"?a:(a==null?void 0:a.message)||"";if(r.includes("not found")||r.includes("does not exist")){const t=n.accountType==="learn"?"buyer":"seller";try{const p=await c(G({idToken:s.idToken,role:t})).unwrap();l.auth.registrationSuccess(),o(t==="buyer"?"/buyer/dashboard":t==="seller"?"/seller-onboarding":"/")}catch(p){throw p}}else throw a}}catch(s){console.error("Google sign-up error:",s);const a=typeof s=="string"?s:(s==null?void 0:s.message)||"Failed to sign up with Google. Please try again.";l.error(a)}};return e.jsx("div",{className:"signup__page",children:e.jsxs("div",{className:"signup__container",children:[e.jsx("h1",{className:"signup__title",children:"Sign up to your account"}),e.jsxs("div",{className:"signup__account-type",children:[e.jsx("p",{className:"signup__label",children:"Select Account Type"}),e.jsxs("div",{className:"signup__options",children:[e.jsxs("div",{className:`signup__option ${n.accountType==="learn"?"signup__option--selected":""}`,onClick:()=>g("learn"),children:[e.jsx("div",{className:"signup__option-checkbox",children:n.accountType==="learn"&&e.jsx("div",{className:"signup__option-check",children:e.jsx(x,{className:"signup__check-icon"})})}),e.jsxs("div",{className:"signup__option-content",children:[e.jsx("div",{className:"signup__option-icon",children:e.jsx(F,{})}),e.jsx("p",{className:"signup__option-text",children:"I want to learn"})]})]}),e.jsxs("div",{className:`signup__option ${n.accountType==="teach"?"signup__option--selected":""}`,onClick:()=>g("teach"),children:[e.jsx("div",{className:"signup__option-checkbox",children:n.accountType==="teach"&&e.jsx("div",{className:"signup__option-check",children:e.jsx(x,{className:"signup__check-icon"})})}),e.jsxs("div",{className:"signup__option-content",children:[e.jsx("div",{className:"signup__option-icon",children:e.jsx($,{})}),e.jsx("p",{className:"signup__option-text",children:"I want to teach"})]})]})]})]}),e.jsxs("form",{onSubmit:T,className:"signup__form",children:[e.jsxs("div",{className:"signup__form-row",children:[e.jsxs("div",{className:"signup__input-container",children:[e.jsx("div",{className:"signup__input-icon",children:e.jsx(N,{})}),e.jsx("input",{type:"text",id:"firstName",name:"firstName",value:n.firstName,onChange:u,placeholder:"First Name",className:`signup__input ${i.firstName?"signup__input--error":""}`,required:!0}),i.firstName&&e.jsx("p",{className:"signup__error",children:i.firstName})]}),e.jsxs("div",{className:"signup__input-container",children:[e.jsx("div",{className:"signup__input-icon",children:e.jsx(N,{})}),e.jsx("input",{type:"text",id:"lastName",name:"lastName",value:n.lastName,onChange:u,placeholder:"Last Name",className:`signup__input ${i.lastName?"signup__input--error":""}`,required:!0}),i.lastName&&e.jsx("p",{className:"signup__error",children:i.lastName})]})]}),e.jsxs("div",{className:"signup__input-container",children:[e.jsx("div",{className:"signup__input-icon",children:e.jsx(E,{})}),e.jsx("input",{type:"email",id:"email",name:"email",value:n.email,onChange:u,placeholder:"Enter Email Address",className:`signup__input ${i.email?"signup__input--error":""}`,required:!0}),i.email&&e.jsx("p",{className:"signup__error",children:i.email})]}),e.jsxs("div",{className:"signup__form-input signup__phone-container",children:[e.jsxs("div",{className:"signup__phone-wrapper",children:[e.jsx("div",{children:e.jsxs("div",{className:"signup__country-code-select",children:[e.jsx(P,{style:{color:"var(--dark-gray)",fontSize:"var(--heading5)"}}),e.jsxs("select",{value:n.countryCode,onChange:f,className:"selectstylesnone",children:[e.jsx("option",{value:"+91",children:"+91"}),e.jsx("option",{value:"+1",children:"+1"}),e.jsx("option",{value:"+44",children:"+44"}),e.jsx("option",{value:"+61",children:"+61"}),e.jsx("option",{value:"+86",children:"+86"}),e.jsx("option",{value:"+49",children:"+49"}),e.jsx("option",{value:"+33",children:"+33"}),e.jsx("option",{value:"+81",children:"+81"}),e.jsx("option",{value:"+7",children:"+7"}),e.jsx("option",{value:"+55",children:"+55"})]})]})}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:n.phone,onChange:s=>{const a=s.target.value.replace(/\D/g,"");u({target:{name:"phone",value:a}})},placeholder:"00000 00000",className:`signup__form-input signup__phone-input ${i.phone?"signup__input--error":""}`,required:!0,pattern:"[0-9]*"})]}),i.phone&&e.jsx("p",{className:"error-message",children:i.phone})]}),e.jsxs("div",{className:"signup__terms",children:[e.jsx("input",{type:"checkbox",id:"agreeToTerms",name:"agreeToTerms",checked:n.agreeToTerms,onChange:u,className:"signup__checkbox"}),e.jsxs("label",{htmlFor:"agreeToTerms",className:"signup__terms-label",children:["By sign up you agree to our"," ",e.jsx(j,{to:"/terms",className:"signup__terms-link",children:"Terms & Conditions"})]}),i.agreeToTerms&&e.jsx("p",{className:"signup__error",children:i.agreeToTerms})]}),e.jsx("button",{type:"submit",className:"signup__button btn-primary",disabled:d,children:d?"Creating Account...":"Create Your Account"}),e.jsx("div",{className:"signup__divider",children:e.jsx("span",{children:"or"})}),e.jsx(A,{onClick:k,isLoading:d,text:"Sign up with Google",variant:"secondary"}),e.jsxs("p",{className:"signup__login-link mt-10",children:["Do you have an account?"," ",e.jsx(j,{to:"/auth",className:"signup__link",children:"Sign In"})]})]})]})})};export{B as default};
