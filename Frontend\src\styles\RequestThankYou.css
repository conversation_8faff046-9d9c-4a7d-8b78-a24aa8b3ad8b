/* Request Thank You Page Styles */
.request-thank-you-page {
  min-height: calc(100vh - 90px);
  background-color: var(--bg-gray);
  padding: 50px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.request-thank-you-page .request-thank-you-container {
  width: 100%;
  max-width: 900px;
  padding: 0 20px;
}

/* Success Header */
.request-thank-you-page .success-header {
  text-align: center;
  margin-bottom: 40px;
}

.request-thank-you-page .success-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.request-thank-you-page .success-icon img {
  width: 80px;
  height: 80px;
}

.request-thank-you-page .success-title {
  font-size: var(--heading3);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 10px;
}

.request-thank-you-page .success-message {
  font-size: var(--basefont);
  color: var(--dark-gray);
  max-width: 500px;
  margin: 0 auto;
}

/* Request Information Card */
.request-thank-you-page .request-info-card {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  padding: 50px;
  margin-bottom: 30px;
}

.request-thank-you-page .request-info-title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 30px;
  text-align: center;
}

/* Request Details Grid */
.request-thank-you-page .request-details-grid {
  display: grid;
  grid-template-columns: 1fr 1px 1fr;
  gap: 30px;
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 2px solid var(--light-gray);
}

.request-thank-you-page .request-details-grid-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.request-thank-you-page .seller-info-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.request-thank-you-page .request-detail-item,
.request-thank-you-page .seller-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid var(--light-gray);
}

.request-thank-you-page .request-detail-item:last-child,
.request-thank-you-page .seller-detail-item:last-child {
  border-bottom: none;
}

.request-thank-you-page .detail-label {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--dark-gray);
}

.request-thank-you-page .detail-value {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
}

.request-thank-you-page .status-pending {
  color: var(--btn-color);
  background-color: var(--primary-light-color);
  padding: 4px 12px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
}

.request-thank-you-page .rating {
  color: var(--btn-color);
}

/* Vertical Line */
.request-thank-you-page .vertical-line {
  width: 1px;
  background-color: var(--light-gray);
  height: 100%;
  min-height: 200px;
}

/* Request Details Section */
.request-thank-you-page .request-details-section {
  margin-bottom: 40px;
}

.request-thank-you-page .section-title {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 20px;
}

.request-thank-you-page .request-content {
  background-color: var(--bg-gray);
  border-radius: var(--border-radius-medium);
  padding: 25px;
}

.request-thank-you-page .request-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 10px;
}

.request-thank-you-page .request-description {
  font-size: var(--basefont);
  color: var(--dark-gray);
  line-height: 1.6;
}

/* Action Buttons */
.request-thank-you-page .action-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}

.request-thank-you-page .request-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  font-size: var(--basefont);
  font-weight: 500;
  border-radius: var(--border-radius-medium);
  transition: all 0.3s ease;
}

.request-thank-you-page .request-btn:hover {
  background-color: var(--btn-color);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.request-thank-you-page .homepage-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  font-size: var(--basefont);
  font-weight: 600;
  border-radius: var(--border-radius-medium);
  background-color: var(--btn-color);
  color: var(--white);
  transition: all 0.3s ease;
}

.request-thank-you-page .homepage-btn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

/* Responsive Design */
@media (max-width: 768px) {
  .request-thank-you-page {
    padding: 30px 0;
  }

  .request-thank-you-page .request-info-card {
    padding: 30px 20px;
  }

  .request-thank-you-page .request-details-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .request-thank-you-page .vertical-line {
    display: none;
  }

  .request-thank-you-page .action-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .request-thank-you-page .action-buttons .btn {
    width: 100%;
    justify-content: center;
  }

  .request-thank-you-page .success-icon img {
    width: 60px;
    height: 60px;
  }

  .request-thank-you-page .success-title {
    font-size: var(--heading4);
  }

  .request-thank-you-page .request-detail-item,
  .request-thank-you-page .seller-detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

@media (max-width: 480px) {
  .request-thank-you-page .request-info-card {
    padding: 20px 15px;
  }

  .request-thank-you-page .request-content {
    padding: 20px;
  }
}
