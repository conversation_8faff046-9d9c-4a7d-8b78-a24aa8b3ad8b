import{d as Q,e as X,r as p,a2 as U,f as $,j as e,aH as Z,W as C,x as ee,a3 as le,a4 as re,aI as D,aJ as O,ag as ie,ab as se,aK as A,O as R,A as M,a5 as ae,a6 as oe}from"./index-DPaefDjm.js";import{S as ne}from"./SellerLayout-k6HMxGdv.js";import{t as E}from"./toast-jTgG8ilJ.js";const fe=()=>{const x=Q(),{user:m,isLoading:j,isSuccess:L,isError:I,error:k}=X(i=>i.auth),[l,h]=p.useState({firstName:"",lastName:"",email:"",mobile:"",profileImage:"",description:"",experiences:[],minTrainingCost:"",socialLinks:{facebook:"",linkedin:"",twitter:""},sellerProfilePic:"",isOnboardingComplete:!1}),[g,V]=p.useState(null),[w,q]=p.useState(null),[d,F]=p.useState(!1),[N,Y]=p.useState({experienceYears:{}});p.useEffect(()=>{x(U())},[x]),p.useEffect(()=>{var i,r,s,c,t,o;if(m&&m.data){const a=m.data,n=a.sellerInfo||{};h({firstName:a.firstName||"",lastName:a.lastName||"",email:a.email||"",mobile:a.mobile||"",profileImage:n.profilePic||"",description:n.description||"",experiences:n.experiences&&n.experiences.length>0?n.experiences:[{schoolName:"",position:"",fromYear:"",toYear:""}],minTrainingCost:n.minTrainingCost||"",socialLinks:{facebook:((i=n.socialLinks)==null?void 0:i.facebook)||"",linkedin:((r=n.socialLinks)==null?void 0:r.linkedin)||"",twitter:((s=n.socialLinks)==null?void 0:s.twitter)||""},sellerProfilePic:n.profilePic||a.profileImage||"",isOnboardingComplete:n.isOnboardingComplete||!1})}else if(m){const a=m.sellerInfo||{};h({firstName:m.firstName||"",lastName:m.lastName||"",email:m.email||"",mobile:m.mobile||"",profileImage:a.profilePic||"",description:a.description||"",experiences:a.experiences&&a.experiences.length>0?a.experiences:[{schoolName:"",position:"",fromYear:"",toYear:""}],minTrainingCost:a.minTrainingCost||"",socialLinks:{facebook:((c=a.socialLinks)==null?void 0:c.facebook)||"",linkedin:((t=a.socialLinks)==null?void 0:t.linkedin)||"",twitter:((o=a.socialLinks)==null?void 0:o.twitter)||""},sellerProfilePic:a.profilePic||m.profileImage||"",isOnboardingComplete:a.isOnboardingComplete||!1})}},[m]);const[S,v]=p.useState(!1),[B,T]=p.useState(!1);p.useEffect(()=>{S&&L&&!j&&(E.success("Profile updated successfully!"),x($()),v(!1),F(!1),x(U())),S&&I&&k&&(E.error(k.message||"Failed to update profile"),x($()),v(!1))},[L,I,k,j,x,S]);const y=()=>{const i=new Date().getFullYear(),r=1950,s=i,c={};let t=!1;return console.log("=== Seller Profile Year Validation Debug ==="),console.log("Current year:",i),console.log("Max allowed year:",s),console.log("Experiences to validate:",l.experiences),l.experiences.forEach((o,a)=>{const n=parseInt(o.fromYear),_=parseInt(o.toYear),f={};console.log(`Experience ${a}:`,{fromYearString:o.fromYear,toYearString:o.toYear,fromYearParsed:n,toYearParsed:_}),String(o.fromYear||"").trim()?(isNaN(n)||n<r||n>s)&&(f.fromYear=`From year must be between ${r} and ${s}`,t=!0,console.log(`From year error: out of range (${n})`)):(f.fromYear="From year is required",t=!0,console.log("From year error: required")),String(o.toYear||"").trim()?isNaN(_)||_<r||_>s?(f.toYear=`To year must be between ${r} and ${s}`,t=!0,console.log(`To year error: out of range (${_})`)):!isNaN(n)&&_<=n&&(f.toYear="To year must be greater than from year",t=!0,console.log("To year error: less than from year")):(f.toYear="To year is required",t=!0,console.log("To year error: required")),Object.keys(f).length>0&&(c[a]=f,console.log(`Errors for experience ${a}:`,f))}),console.log("Total year errors:",c),console.log("Has errors:",t),t?(Y(o=>({...o,experienceYears:c})),!1):(Y(o=>({...o,experienceYears:{}})),!0)},P=i=>{const{name:r,value:s}=i.target;if(r.startsWith("socialLinks.")){const c=r.split(".")[1];h(t=>({...t,socialLinks:{...t.socialLinks,[c]:s}}))}else h(c=>({...c,[r]:s}))},b=(i,r,s)=>{var c,t;h(o=>({...o,experiences:o.experiences.map((a,n)=>n===i?{...a,[r]:s}:a)})),(r==="fromYear"||r==="toYear")&&(t=(c=N.experienceYears)==null?void 0:c[i])!=null&&t[r]&&Y(o=>({...o,experienceYears:{...o.experienceYears,[i]:{...o.experienceYears[i],[r]:""}}}))},H=()=>{h(i=>({...i,experiences:[...i.experiences,{schoolName:"",position:"",fromYear:"",toYear:""}]}))},W=i=>{l.experiences.length>1&&h(r=>({...r,experiences:r.experiences.filter((s,c)=>c!==i)}))},G=async i=>{if(i.preventDefault(),console.log("Validating experience years before submission..."),!y()){console.log("Year validation failed, preventing submission");return}v(!0);try{let s=l.profileImage;g&&(s=(await x(ae(g)).unwrap()).data.fileUrl);const c=g?s:l.sellerProfilePic||s,t={firstName:l.firstName,lastName:l.lastName,profileImage:s,sellerInfo:{description:l.description,experiences:l.experiences,minTrainingCost:l.minTrainingCost,socialLinks:l.socialLinks,profilePic:c,isOnboardingComplete:l.isOnboardingComplete}};console.log("Updating seller profile with data:",t),console.log("Profile update details:",{profileImage:s,sellerInfoProfilePic:c,existingSellerProfilePic:l.sellerProfilePic,isOnboardingComplete:l.isOnboardingComplete,newImageUploaded:!!g}),x(oe(t))}catch(s){console.error("Profile update error:",s),E.error("Failed to upload image or update profile"),v(!1)}},J=i=>{const r=i.target.files[0];if(r){V(r),T(!1);const s=new FileReader;s.onloadend=()=>{q(s.result)},s.readAsDataURL(r)}},K=()=>{T(!0)},z=()=>{window.confirm("Are you sure you want to delete your account? This action cannot be undone.")};return e.jsx(ne,{children:e.jsxs("div",{className:"SellerProfile",children:[e.jsxs("div",{className:"SellerProfile__header",children:[e.jsx("h2",{className:"SellerProfile__title",children:"Seller Profile"}),e.jsxs("button",{className:"SellerProfile__edit-btn",onClick:()=>F(!d),children:[e.jsx(Z,{})," ",d?"Cancel Edit":"Edit Profile"]})]}),e.jsxs("div",{className:"SellerProfile__container",children:[e.jsx("div",{className:"SellerProfile__left-section",children:e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Basic Information"}),e.jsxs("div",{className:"SellerProfile__form-row",children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(C,{})}),e.jsx("input",{type:"text",name:"firstName",value:l.firstName,onChange:P,placeholder:"First Name",disabled:!d,className:`SellerProfile__input ${d?"":"SellerProfile__input--disabled"}`})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(C,{})}),e.jsx("input",{type:"text",name:"lastName",value:l.lastName,onChange:P,placeholder:"Last Name",disabled:!d,className:`SellerProfile__input ${d?"":"SellerProfile__input--disabled"}`})]})})]}),e.jsxs("div",{className:"SellerProfile__form-row ",children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(ee,{})}),e.jsx("input",{type:"email",name:"email",value:l.email,placeholder:"Email Address",disabled:!0,className:"SellerProfile__input SellerProfile__input--disabled"})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(le,{})}),e.jsx("input",{type:"tel",name:"mobile",value:l.mobile,placeholder:"Mobile Number",disabled:!0,className:"SellerProfile__input SellerProfile__input--disabled"})]})})]})]})}),e.jsx("div",{className:"SellerProfile__right-section",children:e.jsxs("div",{className:"SellerProfile__image-container",children:[e.jsx("h3",{className:"SellerProfile__image-title",children:"Profile Image"}),e.jsx("div",{className:"SellerProfile__image",children:w||l.profileImage&&!B?e.jsx("img",{src:w||re(l.profileImage),alt:"Profile",onError:K}):e.jsx("div",{className:"SellerProfile__placeholder",children:l.firstName&&l.lastName?`${l.firstName.charAt(0)}${l.lastName.charAt(0)}`:e.jsx(C,{className:"SellerProfile__user-icon"})})}),d&&e.jsxs(e.Fragment,{children:[e.jsx("button",{className:"SellerProfile__upload-btn",onClick:()=>document.getElementById("profile-image-upload").click(),children:"Upload Photo"}),e.jsx("input",{type:"file",id:"profile-image-upload",accept:"image/*",onChange:J,style:{display:"none"}})]})]})})]}),e.jsxs("div",{className:"SellerProfile__description-experience-container",children:[e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Description"}),e.jsx("div",{className:"SellerProfile__description-container",children:d?e.jsx("textarea",{name:"description",value:l.description,onChange:P,placeholder:"Enter your professional description...",className:"SellerProfile__textarea",rows:4}):e.jsx("div",{className:"SellerProfile__description-display",children:l.description||"No description provided"})})]}),e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Minimum Training Cost"}),e.jsx("div",{className:"SellerProfile__cost-container",children:d?e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(D,{})}),e.jsx("input",{type:"number",name:"minTrainingCost",value:l.minTrainingCost,onChange:P,placeholder:"Enter amount",className:"SellerProfile__input"})]})}):e.jsxs("div",{className:"SellerProfile__cost-display",children:[e.jsx(D,{className:"SellerProfile__cost-icon"}),e.jsx("span",{className:"SellerProfile__cost-amount",children:l.minTrainingCost?`${l.minTrainingCost}`:"Not specified"})]})})]})]}),e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Experience"}),e.jsx("div",{className:"SellerProfile__experiences",children:d?e.jsxs(e.Fragment,{children:[l.experiences&&l.experiences.length>0?e.jsx("div",{className:"SellerProfile__experiences-grid",children:l.experiences.map((i,r)=>{var s,c,t,o,a,n,_,f;return e.jsxs("div",{className:"SellerProfile__experience-edit-item",children:[e.jsxs("div",{className:"SellerProfile__experience-edit-header",children:[e.jsx(O,{className:"SellerProfile__experience-icon"}),e.jsxs("span",{className:"SellerProfile__experience-number",children:["Experience ",r+1]}),l.experiences.length>1&&e.jsx("button",{type:"button",className:"SellerProfile__remove-btn",onClick:()=>W(r),children:e.jsx(ie,{})})]}),e.jsxs("div",{className:"SellerProfile__experience-form",children:[e.jsxs("div",{className:"SellerProfile__form-row",children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsx("input",{type:"text",placeholder:"Enter School Name",value:i.schoolName,onChange:u=>b(r,"schoolName",u.target.value),className:"SellerProfile__input"})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsx("input",{type:"text",placeholder:"Enter Position",value:i.position,onChange:u=>b(r,"position",u.target.value),className:"SellerProfile__input"})})]}),e.jsxs("div",{className:"SellerProfile__form-row SellerProfile__form-row-email-phone",children:[e.jsxs("div",{className:"SellerProfile__input-field",children:[e.jsx("input",{type:"text",placeholder:"From Year",value:i.fromYear,onChange:u=>b(r,"fromYear",u.target.value),onBlur:()=>y(),className:`SellerProfile__input ${(c=(s=N.experienceYears)==null?void 0:s[r])!=null&&c.fromYear?"SellerProfile__input--error":""}`}),((o=(t=N.experienceYears)==null?void 0:t[r])==null?void 0:o.fromYear)&&e.jsx("div",{className:"SellerProfile__field-error",children:N.experienceYears[r].fromYear})]}),e.jsxs("div",{className:"SellerProfile__input-field",children:[e.jsx("input",{type:"text",placeholder:"To Year",value:i.toYear,onChange:u=>b(r,"toYear",u.target.value),onBlur:()=>y(),className:`SellerProfile__input ${(n=(a=N.experienceYears)==null?void 0:a[r])!=null&&n.toYear?"SellerProfile__input--error":""}`}),((f=(_=N.experienceYears)==null?void 0:_[r])==null?void 0:f.toYear)&&e.jsx("div",{className:"SellerProfile__field-error",children:N.experienceYears[r].toYear})]})]})]})]},r)})}):e.jsx("div",{className:"SellerProfile__no-data",children:"No experience information provided"}),e.jsx("div",{className:"SellerProfile__add-experience-container",children:e.jsxs("button",{type:"button",className:"SellerProfile__add-btn",onClick:H,children:[e.jsx(se,{})," Add More Experience"]})})]}):e.jsx(e.Fragment,{children:l.experiences&&l.experiences.length>0?e.jsx("div",{className:"SellerProfile__experiences-grid",children:l.experiences.map((i,r)=>e.jsxs("div",{className:"SellerProfile__experience-item",children:[e.jsxs("div",{className:"SellerProfile__experience-header",children:[e.jsx(O,{className:"SellerProfile__experience-icon"}),e.jsxs("span",{className:"SellerProfile__experience-number",children:["Experience ",r+1]})]}),e.jsxs("div",{className:"SellerProfile__experience-content",children:[e.jsx("div",{className:"SellerProfile__experience-field",children:i.schoolName||"School Name"}),e.jsx("div",{className:"SellerProfile__experience-field",children:i.position||"Position"}),e.jsxs("div",{className:"SellerProfile__experience-years",children:[e.jsx("span",{children:i.fromYear||"Start Year"}),e.jsx("span",{children:i.toYear||"End Year"})]})]})]},r))}):e.jsx("div",{className:"SellerProfile__no-data",children:"No experience information provided"})})})]}),e.jsxs("div",{className:"SellerProfile__section",children:[e.jsx("h3",{className:"SellerProfile__section-title",children:"Social Media Links"}),e.jsx("div",{className:"SellerProfile__social-links",children:d?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(A,{className:"facebook"})}),e.jsx("input",{type:"url",name:"socialLinks.facebook",value:l.socialLinks.facebook,onChange:P,placeholder:"Facebook URL",className:"SellerProfile__input"})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(R,{className:"linkedin"})}),e.jsx("input",{type:"url",name:"socialLinks.linkedin",value:l.socialLinks.linkedin,onChange:P,placeholder:"LinkedIn URL",className:"SellerProfile__input"})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(M,{className:"twitter"})}),e.jsx("input",{type:"url",name:"socialLinks.twitter",value:l.socialLinks.twitter,onChange:P,placeholder:"Twitter URL",className:"SellerProfile__input"})]})})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"SellerProfile__social-item",children:[e.jsx(A,{className:"SellerProfile__social-icon facebook"}),e.jsx("span",{className:"SellerProfile__social-label",children:"Facebook:"}),e.jsx("span",{className:"SellerProfile__social-value",children:l.socialLinks.facebook?e.jsx("a",{href:l.socialLinks.facebook,target:"_blank",rel:"noopener noreferrer",children:l.socialLinks.facebook}):"Not provided"})]}),e.jsxs("div",{className:"SellerProfile__social-item",children:[e.jsx(R,{className:"SellerProfile__social-icon linkedin"}),e.jsx("span",{className:"SellerProfile__social-label",children:"LinkedIn:"}),e.jsx("span",{className:"SellerProfile__social-value",children:l.socialLinks.linkedin?e.jsx("a",{href:l.socialLinks.linkedin,target:"_blank",rel:"noopener noreferrer",children:l.socialLinks.linkedin}):"Not provided"})]}),e.jsxs("div",{className:"SellerProfile__social-item",children:[e.jsx(M,{className:"SellerProfile__social-icon twitter"}),e.jsx("span",{className:"SellerProfile__social-label",children:"Twitter:"}),e.jsx("span",{className:"SellerProfile__social-value",children:l.socialLinks.twitter?e.jsx("a",{href:l.socialLinks.twitter,target:"_blank",rel:"noopener noreferrer",children:l.socialLinks.twitter}):"Not provided"})]})]})})]}),d&&e.jsxs("div",{className:"SellerProfile__buttons",children:[e.jsx("button",{type:"button",className:"SellerProfile__delete-btn",onClick:z,children:"Delete Account"}),e.jsx("button",{type:"button",className:"SellerProfile__save-btn",onClick:G,disabled:S||j,children:S||j?"Updating...":"Update & Save"})]})]})})};export{fe as default};
