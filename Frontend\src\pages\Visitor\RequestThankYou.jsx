import React from "react";
import { useNavigate } from "react-router-dom";
import { MdHome } from "react-icons/md";
import { FaEye } from "react-icons/fa";
import "../../styles/RequestThankYou.css";
import thankyouIcon from "../../assets/images/thankyou.svg";

const RequestThankYou = () => {
  const navigate = useNavigate();

  // Mock data - in real implementation this would come from props or state
  const requestData = {
    requestId: "#12345678",
    title: "Basketball Training Fundamentals",
    sport: "Basketball",
    contentType: "Video Tutorial",
    budget: "$150.00",
    date: "20 May 2025 | 4:50PM",
    status: "Pending",
    description: "Looking for comprehensive basketball training videos covering basic fundamentals, shooting techniques, and defensive strategies for beginner to intermediate players.",
    seller: "Coach <PERSON>",
    sellerRating: "4.8",
    estimatedDelivery: "3-5 business days"
  };

  const handleGoToRequests = () => {
    navigate("/buyer/account/requests");
  };

  const handleGoToHomepage = () => {
    navigate("/");
  };

  return (
    <div className="request-thank-you-page">
      <div className="request-thank-you-container max-container">
        {/* Success Header */}
        <div className="success-header">
          <div className="success-icon">
            <img src={thankyouIcon} alt="Thank you" />
          </div>
          <h1 className="success-title">Your request is submitted successfully!</h1>
          <p className="success-message">
            We will update you for the request status soon via Email or SMS.
          </p>
        </div>

        {/* Request Information Card */}
        <div className="request-info-card">
          <h2 className="request-info-title">Request Information</h2>

          <div className="request-details-grid">
            <div className="request-details-grid-container">
              <div className="request-detail-item">
                <span className="detail-label">Request Id:</span>
                <span className="detail-value">{requestData.requestId}</span>
              </div>
              <div className="request-detail-item">
                <span className="detail-label">Budget:</span>
                <span className="detail-value">{requestData.budget}</span>
              </div>
              <div className="request-detail-item">
                <span className="detail-label">Date:</span>
                <span className="detail-value">{requestData.date}</span>
              </div>
              <div className="request-detail-item">
                <span className="detail-label">Status:</span>
                <span className="detail-value status-pending">{requestData.status}</span>
              </div>
              <div className="request-detail-item">
                <span className="detail-label">Sport:</span>
                <span className="detail-value">{requestData.sport}</span>
              </div>
              <div className="request-detail-item">
                <span className="detail-label">Content Type:</span>
                <span className="detail-value">{requestData.contentType}</span>
              </div>
            </div>

            {/* Vertical Line */}
            <div className="vertical-line"></div>

            {/* Seller Information */}
            <div className="seller-info-section">
              <div className="seller-detail-item">
                <span className="detail-label">Seller:</span>
                <span className="detail-value">{requestData.seller}</span>
              </div>
              <div className="seller-detail-item">
                <span className="detail-label">Rating:</span>
                <span className="detail-value rating">{requestData.sellerRating} ⭐</span>
              </div>
              <div className="seller-detail-item">
                <span className="detail-label">Est. Delivery:</span>
                <span className="detail-value">{requestData.estimatedDelivery}</span>
              </div>
            </div>
          </div>

          {/* Request Details Section */}
          <div className="request-details-section">
            <h3 className="section-title">Request Details</h3>
            <div className="request-content">
              <div className="request-info-content">
                <div className="request-title-section">
                  <h4 className="request-title">{requestData.title}</h4>
                  <p className="request-description">{requestData.description}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="action-buttons">
            <button 
              type="button" 
              className="btn btn-outline request-btn"
              onClick={handleGoToRequests}
            >
              <FaEye /> View My Requests
            </button>
            <button 
              type="button" 
              className="btn btn-primary homepage-btn"
              onClick={handleGoToHomepage}
            >
              <MdHome /> Go To Homepage
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestThankYou;
